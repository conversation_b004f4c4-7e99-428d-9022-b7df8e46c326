#!/usr/bin/env python3
"""
测试标准响应格式
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def print_response(response, title):
    """打印响应信息"""
    print(f"\n{'='*50}")
    print(f"🧪 {title}")
    print(f"{'='*50}")
    print(f"状态码: {response.status_code}")
    
    try:
        data = response.json()
        print(f"响应格式: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 验证标准响应格式
        if isinstance(data, dict):
            required_fields = ['code', 'message', 'success']
            missing_fields = [field for field in required_fields if field not in data]
            
            if missing_fields:
                print(f"❌ 缺少必需字段: {missing_fields}")
            else:
                print("✅ 响应格式符合标准")
                print(f"   - code: {data.get('code')}")
                print(f"   - message: {data.get('message')}")
                print(f"   - success: {data.get('success')}")
                if 'data' in data:
                    print(f"   - data: 包含数据")
        else:
            print("❌ 响应不是标准格式")
            
    except json.JSONDecodeError:
        print(f"❌ 响应不是有效的JSON: {response.text}")

def test_user_registration():
    """测试用户注册"""
    url = f"{BASE_URL}/api/v1/users/register"
    data = {
        "username": "standardtest",
        "email": "<EMAIL>",
        "password": "testpass123",
        "full_name": "标准测试用户"
    }
    
    response = requests.post(url, json=data)
    print_response(response, "用户注册接口")
    return response

def test_user_login():
    """测试用户登录"""
    url = f"{BASE_URL}/api/v1/users/login"
    data = {
        "username": "standardtest",
        "password": "testpass123"
    }

    response = requests.post(url, json=data)
    print_response(response, "用户登录接口")
    
    if response.status_code == 200:
        try:
            token_data = response.json()
            if 'data' in token_data and 'access_token' in token_data['data']:
                return token_data['data']['access_token']
        except:
            pass
    return None

def test_get_current_user(token):
    """测试获取当前用户信息"""
    if not token:
        print("\n❌ 没有有效的token，跳过测试")
        return
        
    url = f"{BASE_URL}/api/v1/users/me"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    print_response(response, "获取当前用户信息接口")

def test_list_users(token):
    """测试获取用户列表"""
    if not token:
        print("\n❌ 没有有效的token，跳过测试")
        return
        
    url = f"{BASE_URL}/api/v1/users/"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    print_response(response, "获取用户列表接口")

def test_create_product(token):
    """测试创建商品"""
    if not token:
        print("\n❌ 没有有效的token，跳过测试")
        return
        
    url = f"{BASE_URL}/api/v1/products/"
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "title": "标准测试商品",
        "description": "这是一个测试商品",
        "price": 99.99,
        "category": "测试分类"
    }
    
    response = requests.post(url, json=data, headers=headers)
    print_response(response, "创建商品接口")

def test_list_products():
    """测试获取商品列表"""
    url = f"{BASE_URL}/api/v1/products/"
    
    response = requests.get(url)
    print_response(response, "获取商品列表接口")

def test_get_online_users(token):
    """测试获取在线用户"""
    if not token:
        print("\n❌ 没有有效的token，跳过测试")
        return
        
    url = f"{BASE_URL}/api/v1/chat/online-users"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    print_response(response, "获取在线用户接口")

def test_error_response():
    """测试错误响应格式"""
    # 测试用户名重复
    url = f"{BASE_URL}/api/v1/users/register"
    data = {
        "username": "standardtest",  # 重复的用户名
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    response = requests.post(url, json=data)
    print_response(response, "错误响应格式（用户名重复）")

def main():
    """主测试函数"""
    print("🚀 开始测试标准响应格式...")
    
    # 测试用户注册
    test_user_registration()
    
    # 测试用户登录
    token = test_user_login()
    
    # 测试获取当前用户信息
    test_get_current_user(token)
    
    # 测试获取用户列表
    test_list_users(token)
    
    # 测试创建商品
    test_create_product(token)
    
    # 测试获取商品列表
    test_list_products()
    
    # 测试获取在线用户
    test_get_online_users(token)
    
    # 测试错误响应
    test_error_response()
    
    print(f"\n{'='*50}")
    print("🎉 测试完成！")
    print("✅ 所有接口都应该返回标准格式：")
    print("   {")
    print('     "code": 状态码,')
    print('     "message": "响应消息",')
    print('     "data": 响应数据,')
    print('     "success": true/false')
    print("   }")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
