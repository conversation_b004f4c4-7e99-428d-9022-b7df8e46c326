#!/usr/bin/env python3
"""
项目安装脚本
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e.stderr}")
        return False

def check_python_version():
    """检查 Python 版本"""
    print("🐍 检查 Python 版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python 版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要 Python 3.8 或更高版本")
        return False

def install_dependencies():
    """安装依赖"""
    return run_command("pip install -r requirements.txt", "安装 Python 依赖")

def check_mysql():
    """检查 MySQL 是否可用"""
    print("🐬 检查 MySQL...")
    try:
        import pymysql
        print("✅ pymysql 已安装")
        return True
    except ImportError:
        print("❌ pymysql 未安装，请确保 MySQL 开发库已安装")
        return False

def create_env_file():
    """创建环境变量文件"""
    if os.path.exists('.env'):
        print("ℹ️  .env 文件已存在，跳过创建")
        return True
    
    print("📝 创建 .env 文件...")
    env_content = """# 数据库配置
DATABASE_URL=mysql+pymysql://xiaohei:ftGN5JB5TB6yC2jd@************:3306/xiaohei

# JWT 配置
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
APP_NAME=小黑后端服务
DEBUG=True
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ .env 文件创建成功")
        return True
    except Exception as e:
        print(f"❌ .env 文件创建失败: {e}")
        return False

def main():
    """主安装函数"""
    print("🚀 开始安装小黑后端服务...")
    print("=" * 50)
    
    # 检查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建环境变量文件
    if not create_env_file():
        print("⚠️  环境变量文件创建失败，请手动创建")
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    # 检查 MySQL
    if not check_mysql():
        print("⚠️  MySQL 检查失败，请确保已正确安装")
    
    print("\n" + "=" * 50)
    print("🎉 安装完成！")
    print("\n📋 下一步操作：")
    print("1. 运行 'python create_database.py' 测试数据库连接")
    print("2. 运行 'python init_tables.py' 初始化数据库表")
    print("3. 运行 'python start.py' 启动服务")
    print("4. 访问 http://localhost:8000/docs 查看 API 文档")
    print("\n💡 提示：")
    print("- 修改 .env 文件中的 SECRET_KEY 用于生产环境")
    print("- 数据库已配置为远程服务器 ************")
    print("- 确保网络可以访问远程数据库服务器")

if __name__ == "__main__":
    main()
