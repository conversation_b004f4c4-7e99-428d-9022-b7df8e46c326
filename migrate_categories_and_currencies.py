#!/usr/bin/env python3
"""
数据库迁移脚本：添加商品种类表和货币信息表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config import settings
from app.database import get_db
from app import models

def migrate_database():
    """执行数据库迁移"""
    engine = create_engine(settings.database_url)
    
    try:
        with engine.connect() as conn:
            print("开始数据库迁移...")
            
            # 1. 创建商品种类表
            print("1. 创建商品种类表...")
            try:
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS product_categories (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) UNIQUE NOT NULL COMMENT '种类名称',
                        display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
                        icon_url VARCHAR(255) COMMENT '种类图标URL',
                        is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
                        sort_order INT DEFAULT 0 COMMENT '排序顺序',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_category_name (name),
                        INDEX idx_category_active (is_active),
                        INDEX idx_category_sort (sort_order)
                    ) COMMENT='商品种类表'
                """))
                print("   ✓ 商品种类表创建成功")
            except Exception as e:
                print(f"   ✗ 创建商品种类表失败: {e}")
                raise
            
            # 2. 创建货币信息表
            print("2. 创建货币信息表...")
            try:
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS currency_info (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        code VARCHAR(10) UNIQUE NOT NULL COMMENT '货币代码',
                        name VARCHAR(100) NOT NULL COMMENT '货币名称',
                        symbol VARCHAR(10) NOT NULL COMMENT '货币符号',
                        icon_url VARCHAR(255) COMMENT '货币图标URL',
                        is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
                        sort_order INT DEFAULT 0 COMMENT '排序顺序',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_currency_code (code),
                        INDEX idx_currency_active (is_active),
                        INDEX idx_currency_sort (sort_order)
                    ) COMMENT='货币信息表'
                """))
                print("   ✓ 货币信息表创建成功")
            except Exception as e:
                print(f"   ✗ 创建货币信息表失败: {e}")
                raise
            
            # 3. 添加商品表的category_id字段
            print("3. 添加商品表的category_id字段...")
            try:
                conn.execute(text("""
                    ALTER TABLE products 
                    ADD COLUMN category_id INT,
                    ADD FOREIGN KEY (category_id) REFERENCES product_categories(id)
                """))
                print("   ✓ 商品表category_id字段添加成功")
            except Exception as e:
                if "Duplicate column name" in str(e) or "already exists" in str(e):
                    print("   - 商品表category_id字段已存在，跳过")
                else:
                    print(f"   ✗ 添加商品表category_id字段失败: {e}")
                    raise
            
            # 4. 插入默认商品种类数据
            print("4. 插入默认商品种类数据...")
            try:
                # 检查是否已有种类数据
                result = conn.execute(text("SELECT COUNT(*) as count FROM product_categories")).fetchone()
                if result.count == 0:
                    conn.execute(text("""
                        INSERT INTO product_categories (name, display_name, sort_order) VALUES
                        ('Razer', 'Razer雷蛇', 1),
                        ('Steam', 'Steam游戏平台', 2),
                        ('AppleCard', 'Apple Gift Card', 3),
                        ('GooglePlay', 'Google Play', 4),
                        ('Amazon', 'Amazon亚马逊', 5),
                        ('iTunes', 'iTunes', 6),
                        ('PlayStation', 'PlayStation', 7),
                        ('Xbox', 'Xbox', 8),
                        ('Netflix', 'Netflix', 9),
                        ('Spotify', 'Spotify', 10)
                    """))
                    print("   ✓ 默认商品种类数据插入成功")
                else:
                    print("   - 商品种类数据已存在，跳过")
            except Exception as e:
                print(f"   ✗ 插入默认商品种类数据失败: {e}")
                raise
            
            # 5. 插入默认货币信息数据
            print("5. 插入默认货币信息数据...")
            try:
                # 检查是否已有货币数据
                result = conn.execute(text("SELECT COUNT(*) as count FROM currency_info")).fetchone()
                if result.count == 0:
                    conn.execute(text("""
                        INSERT INTO currency_info (code, name, symbol, sort_order) VALUES
                        ('USD', '美元', '$', 1),
                        ('CAD', '加拿大元', 'C$', 2),
                        ('GBP', '英镑', '£', 3),
                        ('EUR', '欧元', '€', 4),
                        ('AUD', '澳大利亚元', 'A$', 5),
                        ('NGN', '尼日利亚奈拉', '₦', 6),
                        ('JPY', '日元', '¥', 7),
                        ('CNY', '人民币', '¥', 8)
                    """))
                    print("   ✓ 默认货币信息数据插入成功")
                else:
                    print("   - 货币信息数据已存在，跳过")
            except Exception as e:
                print(f"   ✗ 插入默认货币信息数据失败: {e}")
                raise
            
            conn.commit()
            print("\n✅ 数据库迁移完成！")
            
            # 显示迁移结果
            print("\n📊 迁移结果:")
            
            # 检查商品种类表
            result = conn.execute(text("SELECT COUNT(*) as count FROM product_categories")).fetchone()
            print(f"   商品种类记录数: {result.count} 条")
            
            # 检查货币信息表
            result = conn.execute(text("SELECT COUNT(*) as count FROM currency_info")).fetchone()
            print(f"   货币信息记录数: {result.count} 条")
            
            # 显示当前种类
            categories = conn.execute(text("SELECT name, display_name FROM product_categories WHERE is_active = 1 ORDER BY sort_order")).fetchall()
            print("   当前商品种类:")
            for category in categories:
                print(f"     - {category[0]}: {category[1]}")
            
            # 显示当前货币
            currencies = conn.execute(text("SELECT code, name, symbol FROM currency_info WHERE is_active = 1 ORDER BY sort_order")).fetchall()
            print("   当前货币信息:")
            for currency in currencies:
                print(f"     - {currency[0]}: {currency[1]} ({currency[2]})")
                
    except Exception as e:
        print(f"\n❌ 数据库迁移失败: {e}")
        raise

if __name__ == "__main__":
    migrate_database()
