# API 响应标准化文档

## 概述

本文档描述了小黑后端服务的标准化 API 响应格式。所有 API 接口都遵循统一的响应结构，确保前端开发的一致性和可预测性。

## 标准响应格式

### 成功响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体的响应数据
  },
  "success": true
}
```

### 错误响应格式

```json
{
  "code": 400,
  "message": "错误信息",
  "data": null,
  "success": false
}
```

### 列表响应格式

```json
{
  "code": 200,
  "message": "获取列表成功",
  "data": [
    // 列表数据
  ],
  "success": true,
  "total": 100,
  "page": 1,
  "page_size": 20
}
```

## 字段说明

| 字段名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `code` | number | 是 | HTTP 状态码 |
| `message` | string | 是 | 响应消息，用于用户提示 |
| `data` | any | 否 | 响应数据，可以是对象、数组或 null |
| `success` | boolean | 是 | 操作是否成功 |
| `total` | number | 否 | 列表总数（仅列表接口） |
| `page` | number | 否 | 当前页码（仅列表接口） |
| `page_size` | number | 否 | 每页大小（仅列表接口） |

## 状态码规范

### 成功状态码
- `200` - 操作成功
- `201` - 创建成功

### 错误状态码
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `500` - 服务器内部错误

## 已实现的接口

### 用户相关接口

#### 1. 用户注册
- **接口**: `POST /api/v1/users/register`
- **成功响应**: 
  ```json
  {
    "code": 201,
    "message": "用户注册成功",
    "data": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "full_name": "测试用户",
      "phone": null,
      "avatar_url": null,
      "role": "user",
      "is_active": true,
      "created_at": "2025-06-26T15:07:38"
    },
    "success": true
  }
  ```

#### 2. 用户登录
- **接口**: `POST /api/v1/users/login`
- **成功响应**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "token_type": "bearer",
      "user": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "测试用户",
        "role": "user"
      }
    },
    "success": true
  }
  ```

#### 3. 获取当前用户信息
- **接口**: `GET /api/v1/users/me`
- **需要认证**: 是

#### 4. 获取用户列表
- **接口**: `GET /api/v1/users/`
- **需要认证**: 是
- **支持分页**: 是

#### 5. 获取指定用户信息
- **接口**: `GET /api/v1/users/{user_id}`
- **需要认证**: 是

### 商品相关接口

#### 1. 创建商品
- **接口**: `POST /api/v1/products/`
- **需要认证**: 是

#### 2. 获取商品列表
- **接口**: `GET /api/v1/products/`
- **支持分页**: 是
- **支持筛选**: 是（分类、搜索、可用性）

### 聊天相关接口

#### 1. 获取在线用户
- **接口**: `GET /api/v1/chat/online-users`
- **需要认证**: 是

#### 2. 检查用户状态
- **接口**: `GET /api/v1/chat/user-status/{user_id}`
- **需要认证**: 是

## 错误处理示例

### 用户名已存在
```json
{
  "code": 400,
  "message": "用户名已存在",
  "data": null,
  "success": false
}
```

### 用户不存在
```json
{
  "code": 404,
  "message": "用户不存在",
  "data": null,
  "success": false
}
```

### 登录失败
```json
{
  "code": 401,
  "message": "用户名或密码错误",
  "data": null,
  "success": false
}
```

## 实现细节

### 响应工具模块
位置：`app/response_utils.py`

提供了以下工具函数：
- `success_response()` - 成功响应
- `error_response()` - 错误响应
- `create_response()` - 创建成功响应
- `update_response()` - 更新成功响应
- `delete_response()` - 删除成功响应
- `list_response()` - 列表响应
- `not_found_response()` - 404 响应
- `forbidden_response()` - 403 响应

### 状态码常量
```python
class StatusCode:
    OK = 200
    CREATED = 201
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_SERVER_ERROR = 500
```

### 响应消息常量
```python
class ResponseMessage:
    SUCCESS = "操作成功"
    USER_CREATED = "用户注册成功"
    LOGIN_SUCCESS = "登录成功"
    LOGIN_FAILED = "用户名或密码错误"
    USER_NOT_FOUND = "用户不存在"
    PRODUCT_CREATED = "商品发布成功"
    # ... 更多消息常量
```

## 测试

运行测试脚本验证响应格式：
```bash
python test_standard_response.py
```

测试脚本会验证：
1. 响应格式是否符合标准
2. 必需字段是否存在
3. 成功和错误响应的正确性
4. 分页信息的完整性

## 注意事项

1. **一致性**: 所有接口必须遵循相同的响应格式
2. **错误处理**: 错误信息应该直接返回标准格式，而不是抛出 HTTPException
3. **分页**: 列表接口应该包含分页信息
4. **消息**: 使用中文消息，便于用户理解
5. **数据转换**: 确保日期时间格式为 ISO 字符串
6. **类型安全**: 确保 price 等数值字段正确转换为 float 类型

## 更新日志

- **2025-06-26**: 完成所有主要接口的标准化改造
  - 用户注册、登录、信息获取
  - 商品创建、列表获取
  - 聊天相关接口
  - 错误响应标准化
