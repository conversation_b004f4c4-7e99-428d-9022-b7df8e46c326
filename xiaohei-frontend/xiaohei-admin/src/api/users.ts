import api from './index'
import type { User, ApiResponse, PaginatedResponse, PaginationParams } from '@/types'

// 获取用户列表
export const getUsers = async (params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<User>>> => {
  const queryParams = new URLSearchParams()

  if (params.page) queryParams.append('page', params.page.toString())
  if (params.size) queryParams.append('size', params.size.toString())
  if (params.search) queryParams.append('search', params.search)

  return await api.get(`/admin/users?${queryParams.toString()}`)
}

// 获取单个用户详情
export const getUserById = async (id: number): Promise<ApiResponse<User>> => {
  return await api.get(`/admin/users/${id}`)
}

// 启用/禁用用户
export const toggleUserStatus = async (id: number, is_active: boolean): Promise<ApiResponse<User>> => {
  return await api.put(`/admin/users/${id}/status`, { is_active })
}

// 设置/取消管理员权限
export const toggleAdminStatus = async (id: number, is_admin: boolean): Promise<ApiResponse<User>> => {
  return await api.put(`/admin/users/${id}/admin`, { is_admin })
}

// 删除用户
export const deleteUser = async (id: number): Promise<ApiResponse<null>> => {
  return await api.delete(`/admin/users/${id}`)
}

// 重置用户密码
export const resetUserPassword = async (id: number, new_password: string): Promise<ApiResponse<null>> => {
  return await api.put(`/admin/users/${id}/password`, { new_password })
}

// 获取用户统计信息
export const getUserStats = async (): Promise<ApiResponse<{
  total_users: number
  active_users: number
  admin_users: number
  new_users_today: number
}>> => {
  return await api.get('/admin/users/stats')
}
