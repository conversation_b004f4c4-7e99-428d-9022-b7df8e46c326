import api from './index'
import type { Transaction, ApiResponse, PaginatedResponse } from '@/types'

// 获取交易列表
export const getTransactions = async (params?: {
  page?: number
  size?: number
  search?: string
  status?: string
  start_date?: string
  end_date?: string
}): Promise<ApiResponse<PaginatedResponse<Transaction>>> => {
  return await api.get('/admin/transactions', { params })
}

// 获取单个交易详情
export const getTransaction = async (transactionId: number): Promise<ApiResponse<Transaction>> => {
  return await api.get(`/admin/transactions/${transactionId}`)
}

// 更新交易状态
export const updateTransactionStatus = async (transactionId: number, status: string): Promise<ApiResponse<Transaction>> => {
  return await api.patch(`/admin/transactions/${transactionId}/status`, { status })
}

// 获取交易统计
export const getTransactionStats = async (params?: {
  start_date?: string
  end_date?: string
}): Promise<ApiResponse<{
  total_transactions: number
  total_amount: number
  completed_transactions: number
  pending_transactions: number
  cancelled_transactions: number
}>> => {
  return await api.get('/admin/transactions/stats', { params })
}
