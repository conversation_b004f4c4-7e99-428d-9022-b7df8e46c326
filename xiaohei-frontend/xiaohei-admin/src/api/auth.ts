import api from './index'
import type { LoginRequest, LoginResponse, User, ApiResponse } from '@/types'

// 管理员登录
export const adminLogin = async (data: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
  const response = await api.post('/api/v1/users/login', data)

  // 验证用户是否为管理员
  if (response.data && response.data.user && response.data.user.role !== 'admin') {
    throw new Error('只有管理员可以访问后台管理系统')
  }

  return response
}

// 获取当前管理员信息
export const getCurrentAdmin = async (): Promise<ApiResponse<User>> => {
  return await api.get('/api/v1/users/me')
}

// 管理员登出
export const adminLogout = () => {
  localStorage.removeItem('admin_token')
  localStorage.removeItem('admin_user')
}

// 检查管理员权限
export const checkAdminPermission = (): boolean => {
  const userStr = localStorage.getItem('admin_user')
  if (!userStr) return false

  try {
    const user = JSON.parse(userStr)
    return user.role === 'admin'
  } catch {
    return false
  }
}

// 获取管理员 token
export const getAdminToken = (): string | null => {
  return localStorage.getItem('admin_token')
}

// 设置管理员信息
export const setAdminInfo = (token: string, user: User) => {
  localStorage.setItem('admin_token', token)
  localStorage.setItem('admin_user', JSON.stringify(user))
}
