import api from './index'
import type { Product, CreateProductRequest, UpdateProductRequest, ApiResponse, PaginatedResponse, PaginationParams } from '@/types'

// 获取商品列表
export const getProducts = async (params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Product>>> => {
  const queryParams = new URLSearchParams()
  
  if (params.page) queryParams.append('page', params.page.toString())
  if (params.size) queryParams.append('size', params.size.toString())
  if (params.search) queryParams.append('search', params.search)
  if (params.category) queryParams.append('category', params.category)
  if (params.status) queryParams.append('status', params.status)
  
  return await api.get(`/admin/products?${queryParams.toString()}`)
}

// 获取单个商品详情
export const getProductById = async (id: number): Promise<ApiResponse<Product>> => {
  return await api.get(`/admin/products/${id}`)
}

// 创建商品（管理员发布）
export const createProduct = async (data: CreateProductRequest): Promise<ApiResponse<Product>> => {
  return await api.post('/admin/products', data)
}

// 更新商品
export const updateProduct = async (id: number, data: UpdateProductRequest): Promise<ApiResponse<Product>> => {
  return await api.put(`/admin/products/${id}`, data)
}

// 删除商品
export const deleteProduct = async (id: number): Promise<ApiResponse<null>> => {
  return await api.delete(`/admin/products/${id}`)
}

// 更新商品状态
export const updateProductStatus = async (productId: number, is_available: boolean): Promise<ApiResponse<Product>> => {
  return await api.put(`/admin/products/${productId}/status`, { is_available })
}

// 批量更新商品状态
export const batchUpdateProductStatus = async (ids: number[], status: string): Promise<ApiResponse<null>> => {
  return await api.put('/admin/products/batch-status', { ids, status })
}

// 获取商品分类列表
export const getProductCategories = async (): Promise<ApiResponse<string[]>> => {
  return await api.get('/admin/products/categories')
}

// 获取商品统计信息
export const getProductStats = async (): Promise<ApiResponse<{
  total_products: number
  active_products: number
  sold_products: number
  inactive_products: number
  products_by_category: Record<string, number>
}>> => {
  return await api.get('/admin/products/stats')
}

// 上传商品图片
export const uploadProductImage = async (file: File): Promise<ApiResponse<{ url: string }>> => {
  const formData = new FormData()
  formData.append('file', file)
  
  return await api.post('/admin/products/upload-image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
