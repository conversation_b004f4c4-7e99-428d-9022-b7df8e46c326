import api from './index'
import type { DashboardStats, Transaction, ApiResponse } from '@/types'

// 获取仪表板统计数据
export const getDashboardStats = async (): Promise<ApiResponse<DashboardStats>> => {
  return await api.get('/admin/dashboard/stats')
}

// 获取最近交易
export const getRecentTransactions = async (limit: number = 10): Promise<ApiResponse<Transaction[]>> => {
  return await api.get(`/admin/dashboard/recent-transactions?limit=${limit}`)
}

// 获取用户增长数据
export const getUserGrowthData = async (days: number = 30): Promise<ApiResponse<{
  dates: string[]
  counts: number[]
}>> => {
  return await api.get(`/admin/dashboard/user-growth?days=${days}`)
}

// 获取商品发布数据
export const getProductPublishData = async (days: number = 30): Promise<ApiResponse<{
  dates: string[]
  counts: number[]
}>> => {
  return await api.get(`/admin/dashboard/product-publish?days=${days}`)
}

// 获取交易数据
export const getTransactionData = async (days: number = 30): Promise<ApiResponse<{
  dates: string[]
  amounts: number[]
  counts: number[]
}>> => {
  return await api.get(`/admin/dashboard/transaction-data?days=${days}`)
}

// 获取分类统计
export const getCategoryStats = async (): Promise<ApiResponse<{
  categories: string[]
  counts: number[]
}>> => {
  return await api.get('/admin/dashboard/category-stats')
}

// 获取系统健康状态
export const getSystemHealth = async (): Promise<ApiResponse<{
  database_status: 'healthy' | 'warning' | 'error'
  websocket_connections: number
  active_users: number
  server_uptime: string
  memory_usage: number
  cpu_usage: number
}>> => {
  return await api.get('/admin/dashboard/system-health')
}
