import api from './index'
import type { ChatMessage, ChatConversation, ApiResponse, PaginationParams, User } from '@/types'

// 获取聊天用户列表（管理员客服功能）
export const getChatUsers = async (): Promise<ApiResponse<User[]>> => {
  return await api.get('/admin/chat/users')
}

// 获取聊天会话列表（管理员客服功能）
export const getChatConversations = async (params: PaginationParams = {}): Promise<ApiResponse<ChatConversation[]>> => {
  const queryParams = new URLSearchParams()
  
  if (params.page) queryParams.append('page', params.page.toString())
  if (params.size) queryParams.append('size', params.size.toString())
  if (params.search) queryParams.append('search', params.search)
  
  return await api.get(`/admin/chat/conversations?${queryParams.toString()}`)
}

// 获取与特定用户的聊天记录
export const getChatMessages = async (userId: number, params: PaginationParams = {}): Promise<ApiResponse<ChatMessage[]>> => {
  const queryParams = new URLSearchParams()
  
  if (params.page) queryParams.append('page', params.page.toString())
  if (params.size) queryParams.append('size', params.size.toString())
  
  return await api.get(`/admin/chat/messages/${userId}?${queryParams.toString()}`)
}

// 管理员发送消息
export const sendAdminMessage = async (receiverId: number, content: string): Promise<ApiResponse<ChatMessage>> => {
  return await api.post('/admin/chat/send', {
    receiver_id: receiverId,
    content,
    message_type: 'text'
  })
}

// 标记消息为已读
export const markMessagesAsRead = async (userId: number): Promise<ApiResponse<null>> => {
  return await api.put(`/admin/chat/mark-read/${userId}`)
}

// 获取未读消息统计
export const getUnreadStats = async (): Promise<ApiResponse<{
  total_unread: number
  conversations_with_unread: number
}>> => {
  return await api.get('/admin/chat/unread-stats')
}

// WebSocket 连接管理
export class AdminChatWebSocket {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000

  constructor(
    private onMessage: (message: ChatMessage) => void,
    private onError: (error: Event) => void = () => {},
    private onOpen: () => void = () => {},
    private onClose: () => void = () => {}
  ) {}

  connect() {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      console.error('No admin token found')
      return
    }

    const wsUrl = `ws://45.137.11.50:8000/ws/admin?token=${token}`
    this.ws = new WebSocket(wsUrl)

    this.ws.onopen = () => {
      console.log('Admin WebSocket connected')
      this.reconnectAttempts = 0
      this.onOpen()
    }

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        this.onMessage(message)
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error)
      }
    }

    this.ws.onclose = () => {
      console.log('Admin WebSocket disconnected')
      this.onClose()
      this.attemptReconnect()
    }

    this.ws.onerror = (error) => {
      console.error('Admin WebSocket error:', error)
      this.onError(error)
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectInterval)
    } else {
      console.error('Max reconnection attempts reached')
    }
  }

  sendMessage(receiverId: number, content: string) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'admin_message',
        receiver_id: receiverId,
        content,
        message_type: 'text'
      }))
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }
}
