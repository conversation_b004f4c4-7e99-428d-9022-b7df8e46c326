<template>
  <div class="edit-product-view">
    <div class="page-header">
      <h1>编辑商品</h1>
      <p>修改商品信息</p>
    </div>

    <el-card v-loading="loading">
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="120px"
        @submit.prevent="handleSubmit"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品标题" prop="title">
              <el-input
                v-model="productForm.title"
                placeholder="请输入商品标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品价格" prop="price">
              <el-input-number
                v-model="productForm.price"
                :min="0.01"
                :precision="2"
                placeholder="请输入商品价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品分类" prop="category">
              <el-select v-model="productForm.category" placeholder="请选择商品分类" style="width: 100%">
                <el-option label="电子产品" value="electronics" />
                <el-option label="服装" value="clothing" />
                <el-option label="书籍" value="books" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品状态" prop="status">
              <el-select v-model="productForm.status" placeholder="请选择商品状态" style="width: 100%">
                <el-option label="上架" value="active" />
                <el-option label="下架" value="inactive" />
                <el-option label="已售出" value="sold" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="productForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入商品描述"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="submitting" @click="handleSubmit">
            {{ submitting ? '保存中...' : '保存修改' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="$router.back()">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { getProduct, updateProduct } from '@/api/products'

const route = useRoute()
const router = useRouter()
const productFormRef = ref<FormInstance>()
const loading = ref(false)
const submitting = ref(false)

const productForm = reactive({
  title: '',
  description: '',
  price: 0,
  category: '',
  status: 'active'
})

const productRules: FormRules = {
  title: [
    { required: true, message: '请输入商品标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' },
    { min: 10, max: 1000, message: '描述长度在 10 到 1000 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '价格必须大于 0', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择商品状态', trigger: 'change' }
  ]
}

const loadProduct = async () => {
  const productId = route.params.id as string
  if (!productId) {
    ElMessage.error('商品ID不存在')
    router.back()
    return
  }

  loading.value = true
  try {
    const response = await getProduct(parseInt(productId))
    if (response.success && response.data) {
      const product = response.data
      productForm.title = product.title
      productForm.description = product.description
      productForm.price = product.price
      productForm.category = product.category
      productForm.status = product.status
    }
  } catch (error) {
    console.error('Failed to load product:', error)
    ElMessage.error('加载商品信息失败')
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!productFormRef.value) return

  try {
    await productFormRef.value.validate()
    
    submitting.value = true

    const productId = route.params.id as string
    const response = await updateProduct(parseInt(productId), productForm)
    
    if (response.success) {
      ElMessage.success('商品更新成功')
      router.push('/products')
    }
  } catch (error: any) {
    console.error('Failed to update product:', error)
    ElMessage.error(error.message || '更新失败')
  } finally {
    submitting.value = false
  }
}

const handleReset = () => {
  loadProduct()
}

onMounted(() => {
  loadProduct()
})
</script>

<style scoped>
.edit-product-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

@media (max-width: 768px) {
  .edit-product-view {
    padding: 12px;
  }
}
</style>
