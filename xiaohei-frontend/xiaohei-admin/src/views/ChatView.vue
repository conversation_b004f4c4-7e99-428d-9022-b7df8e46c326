<template>
  <div class="chat-view">
    <div class="page-header">
      <h1>客服聊天</h1>
      <p>处理用户咨询和客服服务</p>
    </div>

    <el-row :gutter="20" class="chat-container">
      <!-- 用户列表 -->
      <el-col :span="8">
        <el-card class="user-list-card">
          <template #header>
            <div class="card-header">
              <span>用户列表</span>
              <el-badge :value="unreadCount" :hidden="unreadCount === 0" />
            </div>
          </template>
          
          <div class="user-search">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索用户"
              clearable
              @input="filterUsers"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="user-list" v-loading="loadingUsers">
            <div
              v-for="user in filteredUsers"
              :key="user.id"
              class="user-item"
              :class="{ active: selectedUser?.id === user.id }"
              @click="selectUser(user)"
            >
              <el-avatar :size="40">
                {{ user.username.charAt(0).toUpperCase() }}
              </el-avatar>
              <div class="user-info">
                <div class="username">{{ user.username }}</div>
                <div class="last-message">{{ user.lastMessage || '暂无消息' }}</div>
              </div>
              <div class="user-status">
                <el-badge
                  v-if="user.unreadCount > 0"
                  :value="user.unreadCount"
                  class="unread-badge"
                />
                <div class="online-status" :class="{ online: user.isOnline }"></div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 聊天区域 -->
      <el-col :span="16">
        <el-card class="chat-card">
          <template #header v-if="selectedUser">
            <div class="chat-header">
              <el-avatar :size="32">
                {{ selectedUser.username.charAt(0).toUpperCase() }}
              </el-avatar>
              <div class="user-info">
                <span class="username">{{ selectedUser.username }}</span>
                <span class="status" :class="{ online: selectedUser.isOnline }">
                  {{ selectedUser.isOnline ? '在线' : '离线' }}
                </span>
              </div>
            </div>
          </template>

          <div v-if="!selectedUser" class="no-chat">
            <el-empty description="请选择一个用户开始聊天" />
          </div>

          <div v-else class="chat-content">
            <!-- 消息列表 -->
            <div ref="messagesContainer" class="messages-container" v-loading="loadingMessages">
              <div
                v-for="message in messages"
                :key="message.id"
                class="message-item"
                :class="{ 'is-admin': message.sender_type === 'admin' }"
              >
                <div class="message-avatar">
                  <el-avatar :size="32">
                    {{ message.sender_type === 'admin' ? 'A' : message.sender?.username?.charAt(0).toUpperCase() }}
                  </el-avatar>
                </div>
                <div class="message-content">
                  <div class="message-info">
                    <span class="sender-name">
                      {{ message.sender_type === 'admin' ? '管理员' : message.sender?.username }}
                    </span>
                    <span class="message-time">{{ formatTime(message.created_at) }}</span>
                  </div>
                  <div class="message-text">{{ message.content }}</div>
                </div>
              </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
              <el-input
                v-model="newMessage"
                type="textarea"
                :rows="3"
                placeholder="输入消息..."
                @keydown.ctrl.enter="sendMessage"
              />
              <div class="input-actions">
                <span class="tip">Ctrl + Enter 发送</span>
                <el-button type="primary" @click="sendMessage" :disabled="!newMessage.trim()">
                  发送
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getChatUsers, getChatMessages, sendAdminMessage } from '@/api/chat'
import { AdminChatWebSocket } from '@/api/chat'
import type { User, ChatMessage } from '@/types'

interface ChatUser extends User {
  lastMessage?: string
  unreadCount: number
  isOnline: boolean
}

const loadingUsers = ref(false)
const loadingMessages = ref(false)
const searchKeyword = ref('')
const selectedUser = ref<ChatUser | null>(null)
const users = ref<ChatUser[]>([])
const messages = ref<ChatMessage[]>([])
const newMessage = ref('')
const messagesContainer = ref<HTMLElement>()
let chatWebSocket: AdminChatWebSocket | null = null

const unreadCount = computed(() => {
  return users.value.reduce((total, user) => total + user.unreadCount, 0)
})

const filteredUsers = computed(() => {
  if (!searchKeyword.value) return users.value
  return users.value.filter(user =>
    user.username.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const loadUsers = async () => {
  loadingUsers.value = true
  try {
    const response = await getChatUsers()
    if (response.success) {
      users.value = response.data.map(user => ({
        ...user,
        unreadCount: 0,
        isOnline: false
      }))
    }
  } catch (error) {
    console.error('Failed to load users:', error)
  } finally {
    loadingUsers.value = false
  }
}

const selectUser = async (user: ChatUser) => {
  selectedUser.value = user
  user.unreadCount = 0
  await loadMessages(user.id)
}

const loadMessages = async (userId: number) => {
  loadingMessages.value = true
  try {
    const response = await getChatMessages(userId)
    if (response.success) {
      messages.value = response.data
      await nextTick()
      scrollToBottom()
    }
  } catch (error) {
    console.error('Failed to load messages:', error)
  } finally {
    loadingMessages.value = false
  }
}

const sendMessage = async () => {
  if (!selectedUser.value || !newMessage.value.trim()) return

  try {
    const response = await sendAdminMessage(
      selectedUser.value.id,
      newMessage.value.trim()
    )

    if (response.success) {
      newMessage.value = ''
      // 消息会通过 WebSocket 实时更新
    }
  } catch (error) {
    console.error('Failed to send message:', error)
    ElMessage.error('发送消息失败')
  }
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (date.toDateString() === now.toDateString()) { // 今天
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleString('zh-CN', { 
      month: '2-digit', 
      day: '2-digit', 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
}

const filterUsers = () => {
  // 搜索功能已通过计算属性实现
}

const initWebSocket = () => {
  chatWebSocket = new AdminChatWebSocket()
  
  chatWebSocket.onMessage = (message: ChatMessage) => {
    // 更新消息列表
    if (selectedUser.value && 
        (message.sender_id === selectedUser.value.id || message.receiver_id === selectedUser.value.id)) {
      messages.value.push(message)
      nextTick(() => scrollToBottom())
    }
    
    // 更新用户列表中的最后消息
    const user = users.value.find(u => u.id === message.sender_id)
    if (user) {
      user.lastMessage = message.content
      if (!selectedUser.value || selectedUser.value.id !== user.id) {
        user.unreadCount++
      }
    }
  }
  
  chatWebSocket.onUserStatusChange = (userId: number, isOnline: boolean) => {
    const user = users.value.find(u => u.id === userId)
    if (user) {
      user.isOnline = isOnline
    }
  }
  
  chatWebSocket.connect()
}

onMounted(() => {
  loadUsers()
  initWebSocket()
})

onUnmounted(() => {
  if (chatWebSocket) {
    chatWebSocket.disconnect()
  }
})
</script>

<style scoped>
.chat-view {
  padding: 20px;
  height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.chat-container {
  height: calc(100% - 80px);
}

.user-list-card,
.chat-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-search {
  margin-bottom: 16px;
}

.user-list {
  height: calc(100% - 120px);
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: background-color 0.3s;
}

.user-item:hover {
  background-color: #f5f5f5;
}

.user-item.active {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.user-item .user-info {
  flex: 1;
  margin-left: 12px;
  min-width: 0;
}

.user-item .username {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.user-item .last-message {
  font-size: 12px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.online-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ccc;
}

.online-status.online {
  background-color: #52c41a;
}

.chat-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-header .user-info {
  display: flex;
  flex-direction: column;
}

.chat-header .username {
  font-weight: 500;
  color: #333;
}

.chat-header .status {
  font-size: 12px;
  color: #999;
}

.chat-header .status.online {
  color: #52c41a;
}

.no-chat {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-content {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  margin-bottom: 16px;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
}

.message-item.is-admin {
  flex-direction: row-reverse;
}

.message-item.is-admin .message-content {
  align-items: flex-end;
}

.message-item.is-admin .message-text {
  background-color: #1890ff;
  color: white;
}

.message-avatar {
  margin: 0 12px;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
  color: #999;
}

.message-text {
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 8px;
  word-wrap: break-word;
}

.input-area {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.tip {
  font-size: 12px;
  color: #999;
}

@media (max-width: 768px) {
  .chat-view {
    padding: 12px;
  }
  
  .chat-container {
    flex-direction: column;
    height: auto;
  }
  
  .user-list-card {
    height: 300px;
    margin-bottom: 20px;
  }
  
  .chat-card {
    height: 500px;
  }
}
</style>
