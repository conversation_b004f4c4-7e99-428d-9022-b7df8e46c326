<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表板</h1>
      <p>欢迎回来，{{ adminStore.user?.username }}</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon users">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.total_users || 0 }}</h3>
              <p>总用户数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon products">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.total_products || 0 }}</h3>
              <p>总商品数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon transactions">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stats.total_transactions || 0 }}</h3>
              <p>总交易数</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon revenue">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <h3>¥{{ (stats.total_revenue || 0).toLocaleString() }}</h3>
              <p>总收入</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="action-buttons">
            <el-button type="primary" @click="$router.push('/products/create')">
              <el-icon><Plus /></el-icon>
              发布商品
            </el-button>
            <el-button type="success" @click="$router.push('/users')">
              <el-icon><User /></el-icon>
              管理用户
            </el-button>
            <el-button type="warning" @click="$router.push('/chat')">
              <el-icon><ChatDotRound /></el-icon>
              客服聊天
            </el-button>
            <el-button type="info" @click="$router.push('/transactions')">
              <el-icon><Money /></el-icon>
              查看交易
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="20">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <span>最近交易</span>
          </template>
          <div v-loading="loadingTransactions">
            <div v-if="recentTransactions.length === 0" class="empty-state">
              <el-empty description="暂无交易记录" />
            </div>
            <div v-else>
              <div
                v-for="transaction in recentTransactions"
                :key="transaction.id"
                class="transaction-item"
              >
                <div class="transaction-info">
                  <h4>{{ transaction.product.title }}</h4>
                  <p>买家: {{ transaction.buyer.username }}</p>
                  <p>金额: ¥{{ transaction.amount }}</p>
                </div>
                <div class="transaction-status">
                  <el-tag :type="getTransactionStatusType(transaction.status)">
                    {{ getTransactionStatusText(transaction.status) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <span>系统状态</span>
          </template>
          <div v-loading="loadingHealth">
            <div class="system-health">
              <div class="health-item">
                <span>数据库状态:</span>
                <el-tag :type="getHealthStatusType(systemHealth.database)">
                  {{ systemHealth.database || 'unknown' }}
                </el-tag>
              </div>
              <div class="health-item">
                <span>API状态:</span>
                <el-tag :type="getHealthStatusType(systemHealth.api)">
                  {{ systemHealth.api || 'unknown' }}
                </el-tag>
              </div>
              <div class="health-item">
                <span>WebSocket状态:</span>
                <el-tag :type="getHealthStatusType(systemHealth.websocket)">
                  {{ systemHealth.websocket || 'unknown' }}
                </el-tag>
              </div>
              <div class="health-item">
                <span>服务器运行时间:</span>
                <span>{{ systemHealth.server_uptime || 'unknown' }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAdminStore } from '@/stores/admin'
import { getDashboardStats, getRecentTransactions, getSystemHealth } from '@/api/dashboard'
import type { DashboardStats, Transaction } from '@/types'
import {
  User,
  Box,
  Money,
  TrendCharts,
  Plus,
  ChatDotRound
} from '@element-plus/icons-vue'

const adminStore = useAdminStore()

const stats = ref<DashboardStats>({
  total_users: 0,
  total_products: 0,
  total_transactions: 0,
  active_products: 0,
  pending_transactions: 0,
  total_revenue: 0
})

const recentTransactions = ref<Transaction[]>([])
const systemHealth = ref<any>({})
const loadingTransactions = ref(false)
const loadingHealth = ref(false)

const loadDashboardData = async () => {
  try {
    const [statsResponse, transactionsResponse, healthResponse] = await Promise.all([
      getDashboardStats(),
      getRecentTransactions(5),
      getSystemHealth()
    ])

    if (statsResponse.success) {
      const data = statsResponse.data
      stats.value = {
        total_users: data.users?.total || 0,
        total_products: data.products?.total || 0,
        total_transactions: data.transactions?.total || 0,
        active_products: data.products?.available || 0,
        pending_transactions: data.transactions?.today_count || 0,
        total_revenue: data.transactions?.today_amount || 0
      }
    }

    if (transactionsResponse.success) {
      recentTransactions.value = transactionsResponse.data
    }

    if (healthResponse.success) {
      systemHealth.value = healthResponse.data
    }
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
}

const getTransactionStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    paid: 'info',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getTransactionStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待付款',
    paid: '已付款',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getHealthStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    healthy: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return statusMap[status] || 'info'
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 20px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  color: #333;
}

.dashboard-header p {
  margin: 0;
  color: #666;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.users {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.products {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.transactions {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #333;
}

.transaction-info p {
  margin: 0 0 2px 0;
  font-size: 12px;
  color: #666;
}

.system-health {
  space-y: 12px;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.health-item:last-child {
  border-bottom: none;
}

.empty-state {
  padding: 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .dashboard {
    padding: 12px;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .action-buttons .el-button {
    flex: 1;
    min-width: 120px;
  }
}
</style>
