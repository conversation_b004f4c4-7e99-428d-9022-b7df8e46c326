<template>
  <div class="transactions-view">
    <div class="page-header">
      <h1>交易管理</h1>
      <p>查看和管理所有交易记录</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索商品标题或用户"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="交易状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="待付款" value="pending" />
            <el-option label="已付款" value="paid" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="exportTransactions">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total_transactions }}</div>
            <div class="stat-label">总交易数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">¥{{ stats.total_amount.toLocaleString() }}</div>
            <div class="stat-label">总交易额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.completed_transactions }}</div>
            <div class="stat-label">已完成交易</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.pending_transactions }}</div>
            <div class="stat-label">待处理交易</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 交易列表 -->
    <el-card>
      <div v-loading="loading">
        <el-table :data="transactions" stripe>
          <el-table-column prop="id" label="交易ID" width="100" />
          <el-table-column label="商品信息" min-width="200">
            <template #default="{ row }">
              <div class="product-info">
                <el-image
                  :src="row.product.images?.[0] || '/placeholder.jpg'"
                  fit="cover"
                  style="width: 40px; height: 40px; border-radius: 4px; margin-right: 8px;"
                />
                <div>
                  <div class="product-title">{{ row.product.title }}</div>
                  <div class="product-price">¥{{ row.product.price }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="buyer.username" label="买家" width="120" />
          <el-table-column prop="seller.username" label="卖家" width="120" />
          <el-table-column prop="amount" label="交易金额" width="120">
            <template #default="{ row }">
              ¥{{ row.amount }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="viewTransaction(row)">
                查看详情
              </el-button>
              <el-dropdown v-if="row.status === 'pending'" @command="(command) => handleAction(command, row)">
                <el-button size="small" type="primary">
                  操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="complete">标记完成</el-dropdown-item>
                    <el-dropdown-item command="cancel">取消交易</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 交易详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="交易详情" width="600px">
      <div v-if="selectedTransaction">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="交易ID">{{ selectedTransaction.id }}</el-descriptions-item>
          <el-descriptions-item label="交易状态">
            <el-tag :type="getStatusType(selectedTransaction.status)">
              {{ getStatusText(selectedTransaction.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="商品标题">{{ selectedTransaction.product.title }}</el-descriptions-item>
          <el-descriptions-item label="商品价格">¥{{ selectedTransaction.product.price }}</el-descriptions-item>
          <el-descriptions-item label="买家">{{ selectedTransaction.buyer.username }}</el-descriptions-item>
          <el-descriptions-item label="卖家">{{ selectedTransaction.seller.username }}</el-descriptions-item>
          <el-descriptions-item label="交易金额">¥{{ selectedTransaction.amount }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedTransaction.created_at) }}</el-descriptions-item>
          <el-descriptions-item v-if="selectedTransaction.completed_at" label="完成时间">
            {{ formatDate(selectedTransaction.completed_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, ArrowDown } from '@element-plus/icons-vue'
import { getTransactions, updateTransactionStatus } from '@/api/transactions'
import type { Transaction } from '@/types'

const loading = ref(false)
const transactions = ref<Transaction[]>([])
const detailDialogVisible = ref(false)
const selectedTransaction = ref<Transaction | null>(null)

const searchForm = reactive({
  search: '',
  status: '',
  dateRange: null as [string, string] | null
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const stats = reactive({
  total_transactions: 0,
  total_amount: 0,
  completed_transactions: 0,
  pending_transactions: 0
})

const loadTransactions = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      search: searchForm.search || undefined,
      status: searchForm.status || undefined,
      start_date: searchForm.dateRange?.[0],
      end_date: searchForm.dateRange?.[1]
    }

    const response = await getTransactions(params)
    if (response.success) {
      transactions.value = response.data.items
      pagination.total = response.data.total
      
      // 更新统计数据
      stats.total_transactions = response.data.total
      stats.total_amount = response.data.items.reduce((sum, t) => sum + t.amount, 0)
      stats.completed_transactions = response.data.items.filter(t => t.status === 'completed').length
      stats.pending_transactions = response.data.items.filter(t => t.status === 'pending').length
    }
  } catch (error) {
    console.error('Failed to load transactions:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadTransactions()
}

const handleReset = () => {
  searchForm.search = ''
  searchForm.status = ''
  searchForm.dateRange = null
  pagination.page = 1
  loadTransactions()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadTransactions()
}

const handleCurrentChange = () => {
  loadTransactions()
}

const viewTransaction = (transaction: Transaction) => {
  selectedTransaction.value = transaction
  detailDialogVisible.value = true
}

const handleAction = async (command: string, transaction: Transaction) => {
  try {
    let newStatus = ''
    let actionText = ''
    
    if (command === 'complete') {
      newStatus = 'completed'
      actionText = '标记为完成'
    } else if (command === 'cancel') {
      newStatus = 'cancelled'
      actionText = '取消'
    }
    
    await ElMessageBox.confirm(`确定要${actionText}这个交易吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await updateTransactionStatus(transaction.id, newStatus)
    if (response.success) {
      ElMessage.success(`${actionText}成功`)
      loadTransactions()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Failed to update transaction:', error)
    }
  }
}

const exportTransactions = () => {
  ElMessage.info('导出功能开发中...')
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    paid: 'info',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待付款',
    paid: '已付款',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadTransactions()
})
</script>

<style scoped>
.transactions-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.search-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.product-price {
  font-size: 12px;
  color: #999;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .transactions-view {
    padding: 12px;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>
