<template>
  <div class="products-view">
    <div class="page-header">
      <div>
        <h1>商品管理</h1>
        <p>管理系统中的所有商品</p>
      </div>
      <el-button type="primary" @click="$router.push('/products/create')">
        <el-icon><Plus /></el-icon>
        发布商品
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索商品标题"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="商品状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="上架" value="active" />
            <el-option label="下架" value="inactive" />
            <el-option label="已售出" value="sold" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.category" placeholder="商品分类" clearable>
            <el-option label="全部" value="" />
            <el-option label="电子产品" value="electronics" />
            <el-option label="服装" value="clothing" />
            <el-option label="书籍" value="books" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-col>
        <el-col :span="10">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 商品列表 -->
    <el-card>
      <div v-loading="loading">
        <el-table :data="products" stripe>
          <el-table-column label="商品图片" width="100">
            <template #default="{ row }">
              <el-image
                :src="row.images?.[0] || '/placeholder.jpg'"
                :preview-src-list="row.images || []"
                fit="cover"
                style="width: 60px; height: 60px; border-radius: 4px;"
              />
            </template>
          </el-table-column>
          <el-table-column prop="title" label="商品标题" min-width="200" />
          <el-table-column prop="price" label="价格" width="120">
            <template #default="{ row }">
              ¥{{ row.price }}
            </template>
          </el-table-column>
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="seller.username" label="发布者" width="120" />
          <el-table-column prop="created_at" label="发布时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="viewProduct(row)">
                查看
              </el-button>
              <el-button size="small" type="primary" @click="editProduct(row)">
                编辑
              </el-button>
              <el-button
                size="small"
                :type="row.status === 'active' ? 'warning' : 'success'"
                @click="toggleProductStatus(row)"
              >
                {{ row.status === 'active' ? '下架' : '上架' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 商品详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="商品详情" width="600px">
      <div v-if="selectedProduct">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商品标题">{{ selectedProduct.title }}</el-descriptions-item>
          <el-descriptions-item label="价格">¥{{ selectedProduct.price }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ selectedProduct.category }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedProduct.status)">
              {{ getStatusText(selectedProduct.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发布者">{{ selectedProduct.seller?.username }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ formatDate(selectedProduct.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="商品描述" :span="2">
            {{ selectedProduct.description }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedProduct.images?.length" class="product-images">
          <h4>商品图片</h4>
          <div class="image-grid">
            <el-image
              v-for="(image, index) in selectedProduct.images"
              :key="index"
              :src="image"
              :preview-src-list="selectedProduct.images"
              fit="cover"
              style="width: 100px; height: 100px; border-radius: 4px; margin-right: 8px; margin-bottom: 8px;"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { getProducts, updateProductStatus } from '@/api/products'
import type { Product } from '@/types'

const router = useRouter()
const loading = ref(false)
const products = ref<Product[]>([])
const detailDialogVisible = ref(false)
const selectedProduct = ref<Product | null>(null)

const searchForm = reactive({
  search: '',
  status: '',
  category: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const loadProducts = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      search: searchForm.search || undefined,
      status: searchForm.status || undefined,
      category: searchForm.category || undefined
    }

    const response = await getProducts(params)
    if (response.success) {
      products.value = response.data
      pagination.total = response.total
    }
  } catch (error) {
    console.error('Failed to load products:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadProducts()
}

const handleReset = () => {
  searchForm.search = ''
  searchForm.status = ''
  searchForm.category = ''
  pagination.page = 1
  loadProducts()
}

const handleSizeChange = () => {
  pagination.page = 1
  loadProducts()
}

const handleCurrentChange = () => {
  loadProducts()
}

const viewProduct = (product: Product) => {
  selectedProduct.value = product
  detailDialogVisible.value = true
}

const editProduct = (product: Product) => {
  router.push(`/products/${product.id}/edit`)
}

const toggleProductStatus = async (product: Product) => {
  try {
    const newStatus = product.status === 'active' ? 'inactive' : 'active'
    const action = newStatus === 'active' ? '上架' : '下架'
    
    await ElMessageBox.confirm(`确定要${action}商品 ${product.title} 吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await updateProductStatus(product.id, newStatus)
    if (response.success) {
      ElMessage.success(`${action}成功`)
      loadProducts()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Failed to toggle product status:', error)
    }
  }
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'warning',
    sold: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '上架',
    inactive: '下架',
    sold: '已售出'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
.products-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.search-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.product-images {
  margin-top: 20px;
}

.product-images h4 {
  margin: 0 0 12px 0;
  color: #333;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .products-view {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .pagination-wrapper {
    text-align: center;
  }
}
</style>
