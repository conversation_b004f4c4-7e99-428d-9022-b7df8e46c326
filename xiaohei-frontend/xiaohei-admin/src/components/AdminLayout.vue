<template>
  <el-container class="admin-layout">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '240px'" class="sidebar">
      <div class="logo">
        <h2 v-if="!isCollapse">小黑管理后台</h2>
        <h2 v-else>小黑</h2>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <el-menu-item index="/">
          <el-icon><Odometer /></el-icon>
          <template #title>仪表板</template>
        </el-menu-item>
        
        <el-menu-item index="/users">
          <el-icon><User /></el-icon>
          <template #title>用户管理</template>
        </el-menu-item>
        
        <el-sub-menu index="products">
          <template #title>
            <el-icon><Box /></el-icon>
            <span>商品管理</span>
          </template>
          <el-menu-item index="/products">商品列表</el-menu-item>
          <el-menu-item index="/products/create">发布商品</el-menu-item>
        </el-sub-menu>
        
        <el-menu-item index="/chat">
          <el-icon><ChatDotRound /></el-icon>
          <template #title>客服聊天</template>
        </el-menu-item>
        
        <el-menu-item index="/transactions">
          <el-icon><Money /></el-icon>
          <template #title>交易管理</template>
        </el-menu-item>
      </el-menu>
    </el-aside>
    
    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            text
            @click="toggleCollapse"
            class="collapse-btn"
          >
            <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
          </el-button>
          
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 未读消息提醒 -->
          <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="message-badge">
            <el-button text @click="$router.push('/chat')">
              <el-icon><Bell /></el-icon>
            </el-button>
          </el-badge>
          
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :size="32">
                {{ adminStore.user?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <span class="username">{{ adminStore.user?.username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <!-- 主内容 -->
      <el-main class="main-content">
        <slot />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useAdminStore } from '@/stores/admin'
import {
  Odometer,
  User,
  Box,
  ChatDotRound,
  Money,
  Expand,
  Fold,
  Bell,
  ArrowDown
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const adminStore = useAdminStore()

const isCollapse = ref(false)
const unreadCount = ref(0)

// 当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path
  if (path.startsWith('/products')) {
    return path === '/products/create' ? '/products/create' : '/products'
  }
  return path
})

// 面包屑导航
const breadcrumbs = computed(() => {
  const path = route.path
  const breadcrumbs = [{ title: '首页', path: '/' }]
  
  if (path === '/users') {
    breadcrumbs.push({ title: '用户管理', path: '/users' })
  } else if (path === '/products') {
    breadcrumbs.push({ title: '商品管理', path: '/products' })
  } else if (path === '/products/create') {
    breadcrumbs.push({ title: '商品管理', path: '/products' })
    breadcrumbs.push({ title: '发布商品', path: '/products/create' })
  } else if (path.startsWith('/products/') && path.includes('/edit')) {
    breadcrumbs.push({ title: '商品管理', path: '/products' })
    breadcrumbs.push({ title: '编辑商品', path: path })
  } else if (path === '/chat') {
    breadcrumbs.push({ title: '客服聊天', path: '/chat' })
  } else if (path === '/transactions') {
    breadcrumbs.push({ title: '交易管理', path: '/transactions' })
  }
  
  return breadcrumbs
})

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const handleUserCommand = async (command: string) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      adminStore.logout()
      ElMessage.success('已退出登录')
      router.push('/login')
    } catch {
      // 用户取消
    }
  } else if (command === 'profile') {
    ElMessage.info('个人资料功能开发中...')
  }
}

// 监听路由变化，更新未读消息数量
watch(route, () => {
  // TODO: 实际项目中应该从 API 获取未读消息数量
  // 这里只是示例
}, { immediate: true })
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.sidebar {
  background-color: #001529;
  transition: width 0.3s;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-bottom: 1px solid #1f1f1f;
}

.logo h2 {
  font-size: 18px;
  font-weight: 600;
}

.sidebar-menu {
  border-right: none;
  background-color: #001529;
}

:deep(.el-menu-item) {
  color: rgba(255, 255, 255, 0.65);
}

:deep(.el-menu-item:hover) {
  background-color: #1890ff !important;
  color: white;
}

:deep(.el-menu-item.is-active) {
  background-color: #1890ff !important;
  color: white;
}

:deep(.el-sub-menu__title) {
  color: rgba(255, 255, 255, 0.65);
}

:deep(.el-sub-menu__title:hover) {
  background-color: #1f1f1f !important;
  color: white;
}

.header {
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.collapse-btn {
  font-size: 18px;
  color: #666;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.message-badge {
  margin-right: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.username {
  font-size: 14px;
  color: #333;
}

.main-content {
  background-color: #f5f5f5;
  padding: 0;
  overflow-y: auto;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #666;
}
</style>
