import { createRouter, createWebHistory } from 'vue-router'
import { useAdminStore } from '@/stores/admin'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'Dashboard',
      component: () => import('@/views/DashboardView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/users',
      name: 'Users',
      component: () => import('@/views/UsersView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/products',
      name: 'Products',
      component: () => import('@/views/ProductsView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/products/create',
      name: 'CreateProduct',
      component: () => import('@/views/CreateProductView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/products/:id/edit',
      name: 'EditProduct',
      component: () => import('@/views/EditProductView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/chat',
      name: 'Chat',
      component: () => import('@/views/ChatView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/transactions',
      name: 'Transactions',
      component: () => import('@/views/TransactionsView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFoundView.vue')
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const adminStore = useAdminStore()

  // 初始化管理员信息
  if (!adminStore.isLoggedIn) {
    adminStore.initAdmin()
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!adminStore.isLoggedIn) {
      next('/login')
      return
    }

    // 检查是否需要管理员权限
    if (to.meta.requiresAdmin && !adminStore.isAdmin) {
      next('/login')
      return
    }
  }

  // 如果已登录且访问登录页，重定向到首页
  if (to.name === 'Login' && adminStore.isLoggedIn) {
    next('/')
    return
  }

  next()
})

export default router
