import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { User } from '@/types'
import { adminLogin, getCurrentAdmin, adminLogout, checkAdminPermission, getAdminToken, setAdminInfo } from '@/api/auth'

export const useAdminStore = defineStore('admin', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.is_admin === true)

  // 初始化管理员信息
  const initAdmin = () => {
    const savedToken = getAdminToken()
    const savedUser = localStorage.getItem('admin_user')

    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('Failed to parse saved admin user:', error)
        logout()
      }
    }
  }

  // 登录
  const login = async (username: string, password: string) => {
    isLoading.value = true
    try {
      const response = await adminLogin({ username, password })

      if (response.success && response.data) {
        token.value = response.data.access_token
        user.value = response.data.user

        // 保存到本地存储
        setAdminInfo(response.data.access_token, response.data.user)

        return response
      } else {
        throw new Error(response.message || '登录失败')
      }
    } finally {
      isLoading.value = false
    }
  }

  // 获取当前管理员信息
  const fetchCurrentAdmin = async () => {
    if (!token.value) return

    try {
      const response = await getCurrentAdmin()
      if (response.success && response.data) {
        user.value = response.data
        localStorage.setItem('admin_user', JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('Failed to fetch current admin:', error)
      logout()
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    adminLogout()
  }

  // 检查管理员权限
  const hasAdminPermission = () => {
    return checkAdminPermission()
  }

  return {
    user,
    token,
    isLoading,
    isLoggedIn,
    isAdmin,
    initAdmin,
    login,
    fetchCurrentAdmin,
    logout,
    hasAdminPermission
  }
})
