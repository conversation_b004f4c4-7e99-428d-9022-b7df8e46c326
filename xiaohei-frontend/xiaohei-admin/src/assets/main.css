@import './base.css';

/* 管理后台全屏布局 */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  font-weight: normal;
  overflow: hidden;
}

/* 重置Element Plus的一些默认样式 */
.el-container {
  height: 100%;
}

.el-aside {
  height: 100vh;
}

.el-main {
  height: calc(100vh - 60px); /* 减去header高度 */
  overflow-y: auto;
}

/* 链接样式保持不变 */
a {
  text-decoration: none;
  color: #409eff;
  transition: 0.4s;
}

@media (hover: hover) {
  a:hover {
    color: #66b1ff;
  }
}
