// 标准 API 响应格式
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
  code: number
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  phone?: string
  avatar_url?: string
  role: 'user' | 'admin'
  is_active: boolean
  created_at: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  user: User
}

// 商品相关类型
export interface Product {
  id: number
  title: string
  description: string
  price: number
  category: string
  condition: string
  location: string
  images: string[]
  seller_id: number
  seller: User
  status: 'active' | 'sold' | 'inactive'
  created_at: string
  updated_at: string
}

export interface CreateProductRequest {
  title: string
  description: string
  price: number
  category: string
  condition: string
  location: string
  images: string[]
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {
  status?: 'active' | 'sold' | 'inactive'
}

// 聊天相关类型
export interface ChatMessage {
  id: number
  sender_id: number
  receiver_id: number
  content: string
  message_type: 'text' | 'image'
  created_at: string
  sender: User
  receiver: User
}

export interface ChatConversation {
  user: User
  last_message: ChatMessage
  unread_count: number
}

// 交易相关类型
export interface Transaction {
  id: number
  product_id: number
  buyer_id: number
  seller_id: number
  amount: number
  status: 'pending' | 'paid' | 'completed' | 'cancelled'
  created_at: string
  updated_at: string
  product: Product
  buyer: User
  seller: User
}

// 统计数据类型
export interface DashboardStats {
  total_users: number
  total_products: number
  total_transactions: number
  active_products: number
  pending_transactions: number
  total_revenue: number
}

// 分页相关类型
export interface PaginationParams {
  page?: number
  size?: number
  search?: string
  category?: string
  status?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 表单验证规则类型
export interface FormRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (rule: any, value: any, callback: any) => void
}
