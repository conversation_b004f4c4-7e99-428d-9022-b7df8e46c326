import type { CSSProperties, ExtractPropTypes } from 'vue';
import type { TooltipV2Sides } from './common';
export declare const tooltipV2ArrowProps: {
    readonly width: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    readonly height: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 10, boolean>;
    readonly style: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => CSSProperties) | (() => CSSProperties | null) | ((new (...args: any[]) => CSSProperties) | (() => CSSProperties | null))[], unknown, unknown, null, boolean>;
};
export declare const tooltipV2ArrowSpecialProps: {
    readonly side: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => TooltipV2Sides) | (() => TooltipV2Sides) | ((new (...args: any[]) => TooltipV2Sides) | (() => TooltipV2Sides))[], TooltipV2Sides, unknown>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
};
export type TooltipV2ArrowProps = ExtractPropTypes<typeof tooltipV2ArrowProps>;
