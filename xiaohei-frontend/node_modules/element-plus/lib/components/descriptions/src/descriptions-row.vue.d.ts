declare const _default: import("vue").DefineComponent<{
    readonly row: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("element-plus").DescriptionItemVNode[]) | (() => import("element-plus").DescriptionItemVNode[]) | ((new (...args: any[]) => import("element-plus").DescriptionItemVNode[]) | (() => import("element-plus").DescriptionItemVNode[]))[], unknown, unknown, () => never[], boolean>;
}, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly row: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("element-plus").DescriptionItemVNode[]) | (() => import("element-plus").DescriptionItemVNode[]) | ((new (...args: any[]) => import("element-plus").DescriptionItemVNode[]) | (() => import("element-plus").DescriptionItemVNode[]))[], unknown, unknown, () => never[], boolean>;
}>>, {
    readonly row: import("element-plus").DescriptionItemVNode[];
}>;
export default _default;
