import type { ExtractPropTypes } from 'vue';
export declare const emptyProps: {
    readonly image: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly imageSize: NumberConstructor;
    readonly description: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
};
export type EmptyProps = ExtractPropTypes<typeof emptyProps>;
