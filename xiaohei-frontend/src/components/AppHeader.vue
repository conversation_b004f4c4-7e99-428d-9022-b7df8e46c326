<template>
  <el-header class="app-header">
    <div class="header-content">
      <!-- Logo 和标题 -->
      <div class="logo-section" @click="$router.push('/home')">
        <h1>小黑交易平台</h1>
      </div>

      <!-- 导航菜单 -->
      <el-menu
        :default-active="activeIndex"
        class="header-menu"
        mode="horizontal"
        @select="handleSelect"
      >
        <el-menu-item index="/home">首页</el-menu-item>
        <el-menu-item index="/products">商品</el-menu-item>
        <el-menu-item index="/my-products" v-if="userStore.isLoggedIn">我的商品</el-menu-item>
        <el-menu-item index="/chat" v-if="userStore.isLoggedIn">
          聊天
          <el-badge :value="chatStore.onlineUserCount" v-if="chatStore.onlineUserCount > 0" class="chat-badge" />
        </el-menu-item>
        <el-menu-item index="/transactions" v-if="userStore.isLoggedIn">交易</el-menu-item>
      </el-menu>

      <!-- 用户操作区域 -->
      <div class="user-section">
        <template v-if="userStore.isLoggedIn">
          <!-- 在线状态指示器 -->
          <el-badge :is-dot="chatStore.isConnected" class="online-indicator">
            <el-icon><Connection /></el-icon>
          </el-badge>
          
          <!-- 用户下拉菜单 -->
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" :src="userStore.user?.avatar_url">
                <el-icon><UserFilled /></el-icon>
              </el-avatar>
              <span class="username">{{ userStore.user?.username }}</span>
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="create-product">
                  <el-icon><Plus /></el-icon>
                  发布商品
                </el-dropdown-item>
                <el-dropdown-item command="users" v-if="userStore.isAdmin">
                  <el-icon><UserFilled /></el-icon>
                  用户管理
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        
        <template v-else>
          <el-button type="primary" @click="$router.push('/login')">
            登录
          </el-button>
          <el-button @click="$router.push('/register')">
            注册
          </el-button>
        </template>
      </div>
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  User, 
  UserFilled, 
  Plus, 
  SwitchButton, 
  ArrowDown,
  Connection
} from '@element-plus/icons-vue'
import { useUserStore, useChatStore } from '@/stores'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const chatStore = useChatStore()

// 当前激活的菜单项
const activeIndex = computed(() => route.path)

// 处理菜单选择
const handleSelect = (key: string) => {
  router.push(key)
}

// 处理用户下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'create-product':
      router.push('/create-product')
      break
    case 'users':
      router.push('/users')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        // 断开聊天连接
        chatStore.clearChatData()
        
        // 退出登录
        userStore.logout()
        
        ElMessage.success('已退出登录')
        router.push('/home')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped>
.app-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
  height: 60px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo-section {
  cursor: pointer;
  transition: opacity 0.3s;
}

.logo-section:hover {
  opacity: 0.8;
}

.logo-section h1 {
  margin: 0;
  color: #409eff;
  font-size: 20px;
  font-weight: 600;
}

.header-menu {
  flex: 1;
  margin: 0 40px;
  border-bottom: none;
}

.header-menu .el-menu-item {
  border-bottom: none;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.online-indicator {
  color: #67c23a;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.chat-badge {
  margin-left: 4px;
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .header-menu {
    margin: 0 20px;
  }
  
  .username {
    display: none;
  }
}

@media (max-width: 576px) {
  .logo-section h1 {
    font-size: 16px;
  }
  
  .header-menu {
    margin: 0 10px;
  }
  
  .header-menu .el-menu-item {
    padding: 0 10px;
    font-size: 14px;
  }
}
</style>
