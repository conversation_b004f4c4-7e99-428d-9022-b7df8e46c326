<template>
  <div class="create-product-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>{{ isEdit ? '编辑商品' : '发布商品' }}</h2>
        </div>
      </template>
      
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="商品标题" prop="title">
          <el-input
            v-model="productForm.title"
            placeholder="请输入商品标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="商品价格" prop="price">
          <el-input-number
            v-model="productForm.price"
            :min="0"
            :precision="2"
            placeholder="请输入商品价格"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="商品分类" prop="category">
          <el-select
            v-model="productForm.category"
            placeholder="请选择商品分类"
            filterable
            allow-create
            style="width: 100%"
          >
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="商品图片" prop="image_url">
          <el-input
            v-model="productForm.image_url"
            placeholder="请输入图片URL（可选）"
          />
          <div class="image-preview" v-if="productForm.image_url">
            <img :src="productForm.image_url" alt="商品图片预览" @error="handleImageError" />
          </div>
        </el-form-item>
        
        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="productForm.description"
            type="textarea"
            :rows="6"
            placeholder="请输入商品描述（可选）"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item v-if="isEdit" label="商品状态" prop="is_available">
          <el-switch
            v-model="productForm.is_available"
            active-text="在售"
            inactive-text="下架"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit"
          >
            {{ isEdit ? '保存修改' : '发布商品' }}
          </el-button>
          <el-button @click="handleCancel">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { 
  createProduct, 
  updateProduct, 
  getProductById, 
  getCategories 
} from '@/api/product'
import type { ProductCreate, ProductUpdate } from '@/types'

const route = useRoute()
const router = useRouter()

// 状态
const loading = ref(false)
const categories = ref<string[]>([])

// 表单引用
const productFormRef = ref<FormInstance>()

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据
const productForm = reactive<ProductCreate & { is_available?: boolean }>({
  title: '',
  price: 0,
  category: '',
  image_url: '',
  description: '',
  is_available: true
})

// 表单验证规则
const productRules: FormRules = {
  title: [
    { required: true, message: '请输入商品标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ]
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await getCategories()
    categories.value = response.data || []
  } catch (error: any) {
    console.error('获取分类列表失败:', error)
  }
}

// 获取商品详情（编辑模式）
const fetchProduct = async () => {
  const productId = Number(route.params.id)
  if (!productId) return
  
  loading.value = true
  try {
    const response = await getProductById(productId)
    const product = response.data
    
    // 填充表单
    productForm.title = product.title
    productForm.price = Number(product.price)
    productForm.category = product.category || ''
    productForm.image_url = product.image_url || ''
    productForm.description = product.description || ''
    productForm.is_available = product.is_available
  } catch (error: any) {
    console.error('获取商品详情失败:', error)
    ElMessage.error('获取商品详情失败')
    router.push('/my-products')
  } finally {
    loading.value = false
  }
}

// 处理图片加载错误
const handleImageError = () => {
  ElMessage.warning('图片加载失败，请检查图片URL是否正确')
}

// 处理表单提交
const handleSubmit = async () => {
  if (!productFormRef.value) return
  
  try {
    await productFormRef.value.validate()
    
    loading.value = true
    
    if (isEdit.value) {
      // 编辑模式
      const productId = Number(route.params.id)
      const updateData: ProductUpdate = {
        title: productForm.title,
        price: productForm.price,
        category: productForm.category || undefined,
        image_url: productForm.image_url || undefined,
        description: productForm.description || undefined,
        is_available: productForm.is_available
      }
      
      await updateProduct(productId, updateData)
      ElMessage.success('商品更新成功')
      router.push(`/products/${productId}`)
    } else {
      // 创建模式
      const createData: ProductCreate = {
        title: productForm.title,
        price: productForm.price,
        category: productForm.category || undefined,
        image_url: productForm.image_url || undefined,
        description: productForm.description || undefined
      }
      
      const response = await createProduct(createData)
      ElMessage.success('商品发布成功')
      router.push(`/products/${response.data.id}`)
    }
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  if (isEdit.value) {
    router.push(`/products/${route.params.id}`)
  } else {
    router.push('/my-products')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCategories()
  if (isEdit.value) {
    fetchProduct()
  }
})
</script>

<style scoped>
.create-product-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 0 20px;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0;
  color: #333;
  font-weight: 600;
}

.image-preview {
  margin-top: 12px;
  width: 200px;
  height: 200px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  background: #f5f7fa;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .create-product-container {
    padding: 16px;
  }
  
  .el-form-item .el-button {
    width: 100%;
    margin-bottom: 12px;
  }
}
</style>
