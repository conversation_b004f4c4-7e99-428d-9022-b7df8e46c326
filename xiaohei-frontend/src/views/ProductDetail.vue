<template>
  <div class="product-detail-container" v-loading="loading">
    <el-card v-if="product">
      <el-row :gutter="32">
        <!-- 商品图片 -->
        <el-col :xs="24" :md="12">
          <div class="product-image">
            <img :src="product.image_url || '/placeholder.jpg'" :alt="product.title" />
          </div>
        </el-col>
        
        <!-- 商品信息 -->
        <el-col :xs="24" :md="12">
          <div class="product-info">
            <div class="product-header">
              <h1>{{ product.title }}</h1>
              <div class="product-status">
                <el-tag :type="product.is_available ? 'success' : 'danger'">
                  {{ product.is_available ? '在售' : '已下架' }}
                </el-tag>
              </div>
            </div>
            
            <div class="product-price">
              <span class="price-label">价格：</span>
              <span class="price-value">¥{{ product.price }}</span>
            </div>
            
            <div class="product-category" v-if="product.category">
              <span class="category-label">分类：</span>
              <el-tag>{{ product.category }}</el-tag>
            </div>
            
            <div class="product-description" v-if="product.description">
              <h3>商品描述</h3>
              <p>{{ product.description }}</p>
            </div>
            
            <div class="product-meta">
              <p><strong>发布时间：</strong>{{ formatDate(product.created_at) }}</p>
              <p><strong>商品ID：</strong>{{ product.id }}</p>
            </div>
            
            <!-- 操作按钮 -->
            <div class="product-actions">
              <template v-if="userStore.isLoggedIn">
                <template v-if="isOwner">
                  <el-button type="primary" @click="editProduct">
                    编辑商品
                  </el-button>
                  <el-button 
                    :type="product.is_available ? 'warning' : 'success'"
                    @click="toggleAvailability"
                    :loading="actionLoading"
                  >
                    {{ product.is_available ? '下架商品' : '上架商品' }}
                  </el-button>
                  <el-button type="danger" @click="deleteProduct" :loading="actionLoading">
                    删除商品
                  </el-button>
                </template>
                <template v-else>
                  <el-button 
                    type="primary" 
                    @click="createTransaction"
                    :disabled="!product.is_available"
                    :loading="actionLoading"
                  >
                    购买商品
                  </el-button>
                  <el-button @click="contactSeller">
                    联系卖家
                  </el-button>
                </template>
              </template>
              <template v-else>
                <el-button type="primary" @click="$router.push('/login')">
                  登录后购买
                </el-button>
              </template>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 空状态 -->
    <el-empty v-else-if="!loading" description="商品不存在" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getProductById, updateProduct, deleteProduct as deleteProductApi } from '@/api/product'
import { createTransaction } from '@/api/transaction'
import type { Product } from '@/types'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 状态
const loading = ref(false)
const actionLoading = ref(false)
const product = ref<Product | null>(null)

// 计算属性
const isOwner = computed(() => {
  return userStore.user && product.value && userStore.user.id === product.value.owner_id
})

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取商品详情
const fetchProduct = async () => {
  const productId = Number(route.params.id)
  if (!productId) {
    ElMessage.error('无效的商品ID')
    router.push('/products')
    return
  }
  
  loading.value = true
  try {
    const response = await getProductById(productId)
    product.value = response.data
  } catch (error: any) {
    console.error('获取商品详情失败:', error)
    ElMessage.error('获取商品详情失败')
    router.push('/products')
  } finally {
    loading.value = false
  }
}

// 编辑商品
const editProduct = () => {
  router.push(`/products/${product.value?.id}/edit`)
}

// 切换商品可用性
const toggleAvailability = async () => {
  if (!product.value) return
  
  const action = product.value.is_available ? '下架' : '上架'
  try {
    await ElMessageBox.confirm(
      `确定要${action}这个商品吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    actionLoading.value = true
    await updateProduct(product.value.id, {
      is_available: !product.value.is_available
    })
    
    product.value.is_available = !product.value.is_available
    ElMessage.success(`商品已${action}`)
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}商品失败`)
    }
  } finally {
    actionLoading.value = false
  }
}

// 删除商品
const deleteProduct = async () => {
  if (!product.value) return
  
  try {
    await ElMessageBox.confirm(
      '确定要删除这个商品吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    actionLoading.value = true
    await deleteProductApi(product.value.id)
    
    ElMessage.success('商品已删除')
    router.push('/my-products')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除商品失败')
    }
  } finally {
    actionLoading.value = false
  }
}

// 创建交易
const createTransaction = async () => {
  if (!product.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要购买商品"${product.value.title}"吗？`,
      '确认购买',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    )
    
    actionLoading.value = true
    await createTransaction({
      product_id: product.value.id,
      amount: product.value.price
    })
    
    ElMessage.success('交易创建成功')
    router.push('/transactions')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('创建交易失败')
    }
  } finally {
    actionLoading.value = false
  }
}

// 联系卖家
const contactSeller = () => {
  if (!product.value) return
  router.push(`/chat?user=${product.value.owner_id}`)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchProduct()
})
</script>

<style scoped>
.product-detail-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 20px;
}

.product-image {
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 8px;
  background: #f5f5f5;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 20px 0;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.product-header h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
  flex: 1;
  margin-right: 16px;
}

.product-price {
  margin-bottom: 16px;
}

.price-label {
  color: #666;
  font-size: 16px;
}

.price-value {
  color: #e74c3c;
  font-size: 32px;
  font-weight: 600;
  margin-left: 8px;
}

.product-category {
  margin-bottom: 24px;
}

.category-label {
  color: #666;
  margin-right: 8px;
}

.product-description {
  margin-bottom: 24px;
}

.product-description h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.product-description p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}

.product-meta {
  margin-bottom: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.product-meta p {
  margin: 0 0 8px 0;
  color: #666;
}

.product-meta p:last-child {
  margin-bottom: 0;
}

.product-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .product-detail-container {
    padding: 16px;
  }
  
  .product-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .product-header h1 {
    margin-right: 0;
    margin-bottom: 12px;
    font-size: 24px;
  }
  
  .price-value {
    font-size: 28px;
  }
  
  .product-actions {
    flex-direction: column;
  }
  
  .product-actions .el-button {
    width: 100%;
  }
}
</style>
