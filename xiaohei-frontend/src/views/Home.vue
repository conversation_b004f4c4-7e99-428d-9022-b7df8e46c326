<template>
  <div class="home-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <h1>欢迎来到小黑交易平台</h1>
      <p>安全、便捷的在线交易体验</p>
      <div class="banner-actions" v-if="!userStore.isLoggedIn">
        <el-button type="primary" size="large" @click="$router.push('/register')">
          立即注册
        </el-button>
        <el-button size="large" @click="$router.push('/login')">
          用户登录
        </el-button>
      </div>
    </div>

    <!-- 功能卡片 -->
    <div class="feature-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="feature-card" @click="$router.push('/products')">
            <div class="feature-icon">
              <el-icon size="40"><ShoppingBag /></el-icon>
            </div>
            <h3>商品浏览</h3>
            <p>浏览所有在售商品</p>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6" v-if="userStore.isLoggedIn">
          <el-card class="feature-card" @click="$router.push('/create-product')">
            <div class="feature-icon">
              <el-icon size="40"><Plus /></el-icon>
            </div>
            <h3>发布商品</h3>
            <p>发布您的商品信息</p>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6" v-if="userStore.isLoggedIn">
          <el-card class="feature-card" @click="$router.push('/chat')">
            <div class="feature-icon">
              <el-icon size="40"><ChatDotRound /></el-icon>
            </div>
            <h3>在线聊天</h3>
            <p>与其他用户实时交流</p>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6" v-if="userStore.isLoggedIn">
          <el-card class="feature-card" @click="$router.push('/transactions')">
            <div class="feature-icon">
              <el-icon size="40"><Money /></el-icon>
            </div>
            <h3>交易管理</h3>
            <p>查看和管理您的交易</p>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最新商品 -->
    <div class="latest-products">
      <h2>最新商品</h2>
      <el-row :gutter="20" v-loading="loading">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="product in latestProducts" :key="product.id">
          <el-card class="product-card" @click="$router.push(`/products/${product.id}`)">
            <div class="product-image">
              <img :src="product.image_url || '/placeholder.jpg'" :alt="product.title" />
            </div>
            <div class="product-info">
              <h4>{{ product.title }}</h4>
              <p class="product-price">¥{{ product.price }}</p>
              <p class="product-category" v-if="product.category">{{ product.category }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <div class="view-more">
        <el-button type="primary" @click="$router.push('/products')">
          查看更多商品
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ShoppingBag, Plus, ChatDotRound, Money } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { getProductList } from '@/api/product'
import type { Product } from '@/types'

const userStore = useUserStore()

// 状态
const loading = ref(false)
const latestProducts = ref<Product[]>([])

// 获取最新商品
const fetchLatestProducts = async () => {
  loading.value = true
  try {
    const response = await getProductList({ limit: 8, available_only: true })
    latestProducts.value = response.data || []
  } catch (error: any) {
    console.error('获取最新商品失败:', error)
    ElMessage.error('获取最新商品失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchLatestProducts()
})
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-banner {
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  margin-bottom: 40px;
}

.welcome-banner h1 {
  font-size: 2.5rem;
  margin-bottom: 16px;
  font-weight: 600;
}

.welcome-banner p {
  font-size: 1.2rem;
  margin-bottom: 32px;
  opacity: 0.9;
}

.banner-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.feature-cards {
  margin-bottom: 40px;
}

.feature-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  color: #409eff;
  margin-bottom: 16px;
}

.feature-card h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.feature-card p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.latest-products h2 {
  text-align: center;
  margin-bottom: 32px;
  color: #333;
  font-weight: 600;
}

.product-card {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.product-image {
  height: 200px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 12px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.product-price {
  color: #e74c3c;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.product-category {
  color: #666;
  font-size: 12px;
  margin: 0;
}

.view-more {
  text-align: center;
  margin-top: 32px;
}

@media (max-width: 768px) {
  .welcome-banner h1 {
    font-size: 2rem;
  }
  
  .banner-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .banner-actions .el-button {
    width: 200px;
  }
}
</style>
