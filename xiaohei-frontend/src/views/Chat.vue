<template>
  <div class="chat-container">
    <el-row :gutter="20" style="height: 100%">
      <!-- 在线用户列表 -->
      <el-col :xs="24" :sm="8" :md="6" class="users-panel">
        <el-card class="users-card">
          <template #header>
            <div class="users-header">
              <h3>在线用户 ({{ chatStore.onlineUserCount }})</h3>
              <el-badge :is-dot="chatStore.isConnected" :type="chatStore.isConnected ? 'success' : 'danger'">
                <el-icon><Connection /></el-icon>
              </el-badge>
            </div>
          </template>
          
          <div class="users-list">
            <div
              v-for="user in chatStore.onlineUsers"
              :key="user.id"
              class="user-item"
              :class="{ active: selectedUserId === user.id }"
              @click="selectUser(user.id)"
            >
              <el-avatar :size="32" :src="user.avatar_url">
                <el-icon><UserFilled /></el-icon>
              </el-avatar>
              <span class="username">{{ user.username }}</span>
              <el-badge
                v-if="getUnreadCount(user.id) > 0"
                :value="getUnreadCount(user.id)"
                class="unread-badge"
              />
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 聊天区域 -->
      <el-col :xs="24" :sm="16" :md="18" class="chat-panel">
        <el-card class="chat-card" v-if="selectedUserId">
          <template #header>
            <div class="chat-header">
              <div class="chat-user-info">
                <el-avatar :size="32" :src="selectedUser?.avatar_url">
                  <el-icon><UserFilled /></el-icon>
                </el-avatar>
                <span class="chat-username">{{ selectedUser?.username }}</span>
              </div>
              <el-button size="small" @click="clearChat">
                清空聊天
              </el-button>
            </div>
          </template>
          
          <!-- 消息列表 -->
          <div class="messages-container" ref="messagesContainer">
            <div
              v-for="message in currentMessages"
              :key="message.id"
              class="message-item"
              :class="{ 'own-message': message.sender_id === userStore.user?.id }"
            >
              <div class="message-avatar">
                <el-avatar :size="28" :src="message.sender?.avatar_url">
                  <el-icon><UserFilled /></el-icon>
                </el-avatar>
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="message-sender">{{ message.sender?.username }}</span>
                  <span class="message-time">{{ formatTime(message.created_at) }}</span>
                </div>
                <div class="message-text">{{ message.content }}</div>
              </div>
            </div>
          </div>
          
          <!-- 消息输入 -->
          <div class="message-input">
            <el-input
              v-model="messageText"
              type="textarea"
              :rows="3"
              placeholder="输入消息..."
              @keydown.ctrl.enter="sendMessage"
              @keydown.meta.enter="sendMessage"
            />
            <div class="input-actions">
              <span class="input-tip">Ctrl+Enter 发送</span>
              <el-button
                type="primary"
                :disabled="!messageText.trim() || !chatStore.isConnected"
                @click="sendMessage"
              >
                发送
              </el-button>
            </div>
          </div>
        </el-card>
        
        <!-- 未选择用户状态 -->
        <el-card class="chat-card" v-else>
          <el-empty description="请选择一个用户开始聊天" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Connection, UserFilled } from '@element-plus/icons-vue'
import { useUserStore, useChatStore } from '@/stores'

const route = useRoute()
const userStore = useUserStore()
const chatStore = useChatStore()

// 状态
const selectedUserId = ref<number | null>(null)
const messageText = ref('')
const messagesContainer = ref<HTMLElement>()

// 计算属性
const selectedUser = computed(() => {
  if (!selectedUserId.value) return null
  return chatStore.onlineUsers.find(user => user.id === selectedUserId.value)
})

const currentMessages = computed(() => {
  if (!selectedUserId.value) return []
  return chatStore.getMessagesWithUser(selectedUserId.value)
})

// 格式化时间
const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleString('zh-CN', { 
      month: '2-digit', 
      day: '2-digit', 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
}

// 获取未读消息数量
const getUnreadCount = (userId: number) => {
  return chatStore.getUnreadCount(userId)
}

// 选择用户
const selectUser = (userId: number) => {
  selectedUserId.value = userId
  // 标记消息为已读
  chatStore.markMessagesAsRead(userId)
  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 发送消息
const sendMessage = () => {
  if (!messageText.value.trim() || !selectedUserId.value || !chatStore.isConnected) {
    return
  }
  
  chatStore.sendMessage(selectedUserId.value, messageText.value.trim())
  messageText.value = ''
  
  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 清空聊天
const clearChat = () => {
  if (!selectedUserId.value) return
  chatStore.clearMessagesWithUser(selectedUserId.value)
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 监听消息变化，自动滚动到底部
watch(currentMessages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  // 连接聊天服务
  chatStore.connect()
  
  // 检查URL参数中是否指定了用户
  const userId = route.query.user
  if (userId && typeof userId === 'string') {
    const targetUserId = parseInt(userId)
    if (!isNaN(targetUserId)) {
      selectedUserId.value = targetUserId
    }
  }
})

// 组件卸载时断开连接
onUnmounted(() => {
  chatStore.disconnect()
})
</script>

<style scoped>
.chat-container {
  height: calc(100vh - 60px);
  padding: 20px;
  background: #f5f7fa;
}

.users-panel, .chat-panel {
  height: 100%;
}

.users-card, .chat-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.users-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.users-list {
  flex: 1;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  border-radius: 6px;
  margin-bottom: 4px;
  transition: background-color 0.3s;
  position: relative;
}

.user-item:hover {
  background: #f0f2f5;
}

.user-item.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.user-item .username {
  margin-left: 12px;
  flex: 1;
  color: #333;
  font-size: 14px;
}

.unread-badge {
  position: absolute;
  right: 8px;
  top: 8px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-user-info {
  display: flex;
  align-items: center;
}

.chat-username {
  margin-left: 12px;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  max-height: calc(100vh - 300px);
}

.message-item {
  display: flex;
  margin-bottom: 16px;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-item.own-message .message-content {
  align-items: flex-end;
}

.message-item.own-message .message-text {
  background: #409eff;
  color: white;
}

.message-avatar {
  margin: 0 8px;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #999;
}

.message-sender {
  margin-right: 8px;
}

.message-text {
  background: white;
  padding: 8px 12px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message-input {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.input-tip {
  color: #999;
  font-size: 12px;
}

@media (max-width: 768px) {
  .chat-container {
    padding: 16px;
    height: calc(100vh - 60px);
  }
  
  .users-panel {
    margin-bottom: 16px;
  }
  
  .messages-container {
    max-height: calc(100vh - 400px);
  }
}
</style>
