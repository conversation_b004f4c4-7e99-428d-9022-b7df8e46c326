<template>
  <div class="profile-container">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h2>个人资料</h2>
        </div>
      </template>
      
      <div class="profile-content">
        <!-- 用户头像 -->
        <div class="avatar-section">
          <el-avatar :size="100" :src="userStore.user?.avatar_url">
            <el-icon><UserFilled /></el-icon>
          </el-avatar>
          <el-button type="text" class="change-avatar-btn">
            更换头像
          </el-button>
        </div>
        
        <!-- 用户信息表单 -->
        <el-form
          ref="profileFormRef"
          :model="profileForm"
          :rules="profileRules"
          label-width="100px"
          class="profile-form"
        >
          <el-form-item label="用户名">
            <el-input v-model="userStore.user?.username" disabled />
          </el-form-item>
          
          <el-form-item label="邮箱">
            <el-input v-model="userStore.user?.email" disabled />
          </el-form-item>
          
          <el-form-item label="真实姓名" prop="full_name">
            <el-input
              v-model="profileForm.full_name"
              placeholder="请输入真实姓名"
            />
          </el-form-item>
          
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model="profileForm.phone"
              placeholder="请输入手机号"
            />
          </el-form-item>
          
          <el-form-item label="角色">
            <el-tag :type="userStore.user?.role === 'admin' ? 'danger' : 'primary'">
              {{ userStore.user?.role === 'admin' ? '管理员' : '普通用户' }}
            </el-tag>
          </el-form-item>
          
          <el-form-item label="账号状态">
            <el-tag :type="userStore.user?.is_active ? 'success' : 'danger'">
              {{ userStore.user?.is_active ? '正常' : '已禁用' }}
            </el-tag>
          </el-form-item>
          
          <el-form-item label="注册时间">
            <span>{{ formatDate(userStore.user?.created_at) }}</span>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              :loading="userStore.loading"
              @click="handleUpdateProfile"
            >
              保存修改
            </el-button>
            <el-button @click="resetForm">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { UserFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import type { UserUpdate } from '@/types'

const userStore = useUserStore()

// 表单引用
const profileFormRef = ref<FormInstance>()

// 表单数据
const profileForm = reactive<UserUpdate>({
  full_name: '',
  phone: '',
  avatar_url: ''
})

// 表单验证规则
const profileRules: FormRules = {
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ]
}

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 初始化表单数据
const initForm = () => {
  if (userStore.user) {
    profileForm.full_name = userStore.user.full_name || ''
    profileForm.phone = userStore.user.phone || ''
    profileForm.avatar_url = userStore.user.avatar_url || ''
  }
}

// 重置表单
const resetForm = () => {
  initForm()
}

// 处理更新个人资料
const handleUpdateProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    
    await userStore.updateUser(profileForm)
    
    ElMessage.success('个人资料更新成功')
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

// 组件挂载时初始化
onMounted(() => {
  initForm()
})
</script>

<style scoped>
.profile-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 0 20px;
}

.profile-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0;
  color: #333;
  font-weight: 600;
}

.profile-content {
  display: flex;
  gap: 40px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  min-width: 140px;
}

.change-avatar-btn {
  font-size: 14px;
  color: #409eff;
}

.profile-form {
  flex: 1;
}

@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
    align-items: center;
  }
  
  .profile-form {
    width: 100%;
  }
}
</style>
