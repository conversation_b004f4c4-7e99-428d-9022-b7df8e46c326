<template>
  <div class="my-products-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>我的商品</h1>
      <el-button type="primary" @click="$router.push('/create-product')">
        <el-icon><Plus /></el-icon>
        发布新商品
      </el-button>
    </div>

    <!-- 商品列表 -->
    <div class="products-list" v-loading="loading">
      <el-row :gutter="20">
        <el-col
          :xs="24" :sm="12" :md="8" :lg="6"
          v-for="product in products"
          :key="product.id"
        >
          <el-card class="product-card">
            <div class="product-image" @click="viewProduct(product.id)">
              <img :src="product.image_url || '/placeholder.jpg'" :alt="product.title" />
              <div class="product-status">
                <el-tag :type="product.is_available ? 'success' : 'danger'">
                  {{ product.is_available ? '在售' : '已下架' }}
                </el-tag>
              </div>
            </div>
            
            <div class="product-info">
              <h4 class="product-title" @click="viewProduct(product.id)">
                {{ product.title }}
              </h4>
              <p class="product-description" v-if="product.description">
                {{ product.description }}
              </p>
              <div class="product-meta">
                <span class="product-price">¥{{ product.price }}</span>
                <el-tag size="small" v-if="product.category">{{ product.category }}</el-tag>
              </div>
              <div class="product-date">
                发布于 {{ formatDate(product.created_at) }}
              </div>
            </div>
            
            <div class="product-actions">
              <el-button size="small" @click="editProduct(product.id)">
                编辑
              </el-button>
              <el-button 
                size="small"
                :type="product.is_available ? 'warning' : 'success'"
                @click="toggleAvailability(product)"
              >
                {{ product.is_available ? '下架' : '上架' }}
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="deleteProduct(product)"
              >
                删除
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <el-empty v-if="!loading && products.length === 0" description="您还没有发布任何商品">
        <el-button type="primary" @click="$router.push('/create-product')">
          发布第一个商品
        </el-button>
      </el-empty>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 48]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getMyProducts, updateProduct, deleteProduct as deleteProductApi } from '@/api/product'
import type { Product } from '@/types'

const router = useRouter()

// 状态
const loading = ref(false)
const products = ref<Product[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

// 计算属性
const skip = computed(() => (currentPage.value - 1) * pageSize.value)

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取我的商品列表
const fetchMyProducts = async () => {
  loading.value = true
  try {
    const response = await getMyProducts({
      skip: skip.value,
      limit: pageSize.value
    })
    products.value = response.data || []
    total.value = response.total || 0
  } catch (error: any) {
    console.error('获取我的商品失败:', error)
    ElMessage.error('获取我的商品失败')
  } finally {
    loading.value = false
  }
}

// 查看商品详情
const viewProduct = (productId: number) => {
  router.push(`/products/${productId}`)
}

// 编辑商品
const editProduct = (productId: number) => {
  router.push(`/products/${productId}/edit`)
}

// 切换商品可用性
const toggleAvailability = async (product: Product) => {
  const action = product.is_available ? '下架' : '上架'
  try {
    await ElMessageBox.confirm(
      `确定要${action}商品"${product.title}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await updateProduct(product.id, {
      is_available: !product.is_available
    })
    
    // 更新本地状态
    const index = products.value.findIndex(p => p.id === product.id)
    if (index !== -1) {
      products.value[index].is_available = !products.value[index].is_available
    }
    
    ElMessage.success(`商品已${action}`)
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}商品失败`)
    }
  }
}

// 删除商品
const deleteProduct = async (product: Product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品"${product.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await deleteProductApi(product.id)
    
    // 从列表中移除
    products.value = products.value.filter(p => p.id !== product.id)
    total.value--
    
    ElMessage.success('商品已删除')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除商品失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  fetchMyProducts()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchMyProducts()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchMyProducts()
})
</script>

<style scoped>
.my-products-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.products-list {
  min-height: 400px;
}

.product-card {
  margin-bottom: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 12px;
  cursor: pointer;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-image:hover img {
  transform: scale(1.05);
}

.product-status {
  position: absolute;
  top: 8px;
  right: 8px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-title {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
  cursor: pointer;
  transition: color 0.3s ease;
}

.product-title:hover {
  color: #409eff;
}

.product-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.product-price {
  color: #e74c3c;
  font-size: 18px;
  font-weight: 600;
}

.product-date {
  color: #999;
  font-size: 12px;
  margin-bottom: 12px;
}

.product-actions {
  display: flex;
  gap: 8px;
  margin-top: auto;
}

.product-actions .el-button {
  flex: 1;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

@media (max-width: 768px) {
  .my-products-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .page-header .el-button {
    width: 100%;
  }
}
</style>
