<template>
  <div class="transactions-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>我的交易</h1>
      <div class="header-actions">
        <el-select v-model="statusFilter" placeholder="筛选状态" @change="handleStatusChange" clearable>
          <el-option label="待付款" value="pending" />
          <el-option label="已付款" value="paid" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
        <el-button @click="refreshTransactions" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 交易列表 -->
    <div class="transactions-list" v-loading="loading">
      <el-card
        v-for="transaction in transactions"
        :key="transaction.id"
        class="transaction-card"
      >
        <div class="transaction-header">
          <div class="transaction-info">
            <h3>交易 #{{ transaction.id }}</h3>
            <el-tag :type="getStatusType(transaction.status)">
              {{ getStatusText(transaction.status) }}
            </el-tag>
          </div>
          <div class="transaction-amount">
            ¥{{ transaction.amount }}
          </div>
        </div>
        
        <div class="transaction-content">
          <div class="product-info" v-if="transaction.product">
            <div class="product-image">
              <img :src="transaction.product.image_url || '/placeholder.jpg'" :alt="transaction.product.title" />
            </div>
            <div class="product-details">
              <h4 @click="viewProduct(transaction.product.id)">{{ transaction.product.title }}</h4>
              <p v-if="transaction.product.description">{{ transaction.product.description }}</p>
              <div class="product-meta">
                <span v-if="transaction.product.category">分类：{{ transaction.product.category }}</span>
              </div>
            </div>
          </div>
          
          <div class="transaction-parties">
            <div class="party">
              <span class="party-label">买家：</span>
              <span class="party-name">{{ transaction.buyer?.username }}</span>
            </div>
            <div class="party">
              <span class="party-label">卖家：</span>
              <span class="party-name">{{ transaction.seller?.username }}</span>
            </div>
          </div>
          
          <div class="transaction-meta">
            <p><strong>创建时间：</strong>{{ formatDate(transaction.created_at) }}</p>
            <p v-if="transaction.updated_at !== transaction.created_at">
              <strong>更新时间：</strong>{{ formatDate(transaction.updated_at) }}
            </p>
          </div>
        </div>
        
        <div class="transaction-actions">
          <template v-if="transaction.status === 'pending'">
            <template v-if="isBuyer(transaction)">
              <el-button type="primary" @click="payTransaction(transaction)">
                付款
              </el-button>
              <el-button type="danger" @click="cancelTransaction(transaction)">
                取消交易
              </el-button>
            </template>
            <template v-else>
              <el-button type="info" disabled>
                等待买家付款
              </el-button>
            </template>
          </template>
          
          <template v-else-if="transaction.status === 'paid'">
            <template v-if="isSeller(transaction)">
              <el-button type="success" @click="completeTransaction(transaction)">
                确认完成
              </el-button>
            </template>
            <template v-else>
              <el-button type="info" disabled>
                等待卖家确认
              </el-button>
            </template>
          </template>
          
          <template v-else-if="transaction.status === 'completed'">
            <el-button type="info" disabled>
              交易已完成
            </el-button>
          </template>
          
          <template v-else-if="transaction.status === 'cancelled'">
            <el-button type="info" disabled>
              交易已取消
            </el-button>
          </template>
          
          <!-- 联系对方按钮 -->
          <el-button @click="contactUser(transaction)">
            联系{{ isBuyer(transaction) ? '卖家' : '买家' }}
          </el-button>
        </div>
      </el-card>

      <!-- 空状态 -->
      <el-empty v-if="!loading && transactions.length === 0" description="暂无交易记录" />
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { 
  getTransactionList, 
  updateTransactionStatus 
} from '@/api/transaction'
import type { Transaction } from '@/types'

const router = useRouter()
const userStore = useUserStore()

// 状态
const loading = ref(false)
const transactions = ref<Transaction[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const statusFilter = ref('')

// 计算属性
const skip = computed(() => (currentPage.value - 1) * pageSize.value)

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    paid: 'info',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待付款',
    paid: '已付款',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 判断是否为买家
const isBuyer = (transaction: Transaction) => {
  return userStore.user?.id === transaction.buyer_id
}

// 判断是否为卖家
const isSeller = (transaction: Transaction) => {
  return userStore.user?.id === transaction.seller_id
}

// 获取交易列表
const fetchTransactions = async () => {
  loading.value = true
  try {
    const params = {
      skip: skip.value,
      limit: pageSize.value,
      status: statusFilter.value || undefined
    }
    
    const response = await getTransactionList(params)
    transactions.value = response.data || []
    total.value = response.total || 0
  } catch (error: any) {
    console.error('获取交易列表失败:', error)
    ElMessage.error('获取交易列表失败')
  } finally {
    loading.value = false
  }
}

// 查看商品详情
const viewProduct = (productId: number) => {
  router.push(`/products/${productId}`)
}

// 付款
const payTransaction = async (transaction: Transaction) => {
  try {
    await ElMessageBox.confirm(
      `确定要为交易 #${transaction.id} 付款 ¥${transaction.amount} 吗？`,
      '确认付款',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    )
    
    await updateTransactionStatus(transaction.id, 'paid')
    ElMessage.success('付款成功')
    fetchTransactions()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('付款失败')
    }
  }
}

// 完成交易
const completeTransaction = async (transaction: Transaction) => {
  try {
    await ElMessageBox.confirm(
      `确定要完成交易 #${transaction.id} 吗？`,
      '确认完成',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success',
      }
    )
    
    await updateTransactionStatus(transaction.id, 'completed')
    ElMessage.success('交易已完成')
    fetchTransactions()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('完成交易失败')
    }
  }
}

// 取消交易
const cancelTransaction = async (transaction: Transaction) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消交易 #${transaction.id} 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await updateTransactionStatus(transaction.id, 'cancelled')
    ElMessage.success('交易已取消')
    fetchTransactions()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('取消交易失败')
    }
  }
}

// 联系用户
const contactUser = (transaction: Transaction) => {
  const targetUserId = isBuyer(transaction) ? transaction.seller_id : transaction.buyer_id
  router.push(`/chat?user=${targetUserId}`)
}

// 刷新交易
const refreshTransactions = () => {
  fetchTransactions()
}

// 状态筛选变化
const handleStatusChange = () => {
  currentPage.value = 1
  fetchTransactions()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  fetchTransactions()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchTransactions()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTransactions()
})
</script>

<style scoped>
.transactions-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.transactions-list {
  min-height: 400px;
}

.transaction-card {
  margin-bottom: 20px;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.transaction-info h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.transaction-amount {
  color: #e74c3c;
  font-size: 24px;
  font-weight: 600;
}

.transaction-content {
  margin-bottom: 16px;
}

.product-info {
  display: flex;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.product-image {
  width: 80px;
  height: 80px;
  margin-right: 16px;
  border-radius: 4px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-details h4 {
  margin: 0 0 8px 0;
  color: #333;
  cursor: pointer;
  transition: color 0.3s;
}

.product-details h4:hover {
  color: #409eff;
}

.product-details p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.product-meta {
  color: #999;
  font-size: 12px;
}

.transaction-parties {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.party-label {
  color: #666;
  margin-right: 4px;
}

.party-name {
  color: #333;
  font-weight: 500;
}

.transaction-meta p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.transaction-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

@media (max-width: 768px) {
  .transactions-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
  }
  
  .transaction-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .product-info {
    flex-direction: column;
  }
  
  .product-image {
    width: 100%;
    height: 200px;
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .transaction-parties {
    flex-direction: column;
    gap: 8px;
  }
  
  .transaction-actions {
    flex-direction: column;
  }
  
  .transaction-actions .el-button {
    width: 100%;
  }
}
</style>
