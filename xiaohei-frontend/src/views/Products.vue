<template>
  <div class="products-container">
    <!-- 搜索和筛选区域 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="searchQuery"
            placeholder="搜索商品..."
            :prefix-icon="Search"
            @input="handleSearch"
            clearable
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-select
            v-model="selectedCategory"
            placeholder="选择分类"
            @change="handleCategoryChange"
            clearable
          >
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-switch
            v-model="availableOnly"
            @change="handleAvailableChange"
            active-text="仅显示在售"
            inactive-text="显示全部"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="4">
          <el-button type="primary" @click="refreshProducts" :loading="loading">
            刷新
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 商品列表 -->
    <div class="products-grid" v-loading="loading">
      <el-row :gutter="20">
        <el-col
          :xs="24" :sm="12" :md="8" :lg="6"
          v-for="product in products"
          :key="product.id"
        >
          <el-card class="product-card" @click="viewProduct(product.id)">
            <div class="product-image">
              <img :src="product.image_url || '/placeholder.jpg'" :alt="product.title" />
              <div class="product-status" v-if="!product.is_available">
                <el-tag type="danger">已下架</el-tag>
              </div>
            </div>
            <div class="product-info">
              <h4 class="product-title">{{ product.title }}</h4>
              <p class="product-description" v-if="product.description">
                {{ product.description }}
              </p>
              <div class="product-meta">
                <span class="product-price">¥{{ product.price }}</span>
                <el-tag size="small" v-if="product.category">{{ product.category }}</el-tag>
              </div>
              <div class="product-footer">
                <span class="product-date">{{ formatDate(product.created_at) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <el-empty v-if="!loading && products.length === 0" description="暂无商品" />
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 48, 96]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getProductList, getCategories } from '@/api/product'
import type { Product } from '@/types'

const router = useRouter()

// 状态
const loading = ref(false)
const products = ref<Product[]>([])
const categories = ref<string[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

// 搜索和筛选
const searchQuery = ref('')
const selectedCategory = ref('')
const availableOnly = ref(true)

// 计算属性
const skip = computed(() => (currentPage.value - 1) * pageSize.value)

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取商品列表
const fetchProducts = async () => {
  loading.value = true
  try {
    const params = {
      skip: skip.value,
      limit: pageSize.value,
      search: searchQuery.value || undefined,
      category: selectedCategory.value || undefined,
      available_only: availableOnly.value
    }
    
    const response = await getProductList(params)
    products.value = response.data || []
    total.value = response.total || 0
  } catch (error: any) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await getCategories()
    categories.value = response.data || []
  } catch (error: any) {
    console.error('获取分类列表失败:', error)
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchProducts()
}

// 分类变化处理
const handleCategoryChange = () => {
  currentPage.value = 1
  fetchProducts()
}

// 可用性筛选变化处理
const handleAvailableChange = () => {
  currentPage.value = 1
  fetchProducts()
}

// 刷新商品
const refreshProducts = () => {
  fetchProducts()
}

// 查看商品详情
const viewProduct = (productId: number) => {
  router.push(`/products/${productId}`)
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  fetchProducts()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchProducts()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCategories()
  fetchProducts()
})
</script>

<style scoped>
.products-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.products-grid {
  min-height: 400px;
}

.product-card {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  height: 100%;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 12px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-status {
  position: absolute;
  top: 8px;
  right: 8px;
}

.product-info {
  display: flex;
  flex-direction: column;
  height: calc(100% - 212px);
}

.product-title {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.product-price {
  color: #e74c3c;
  font-size: 18px;
  font-weight: 600;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-date {
  color: #999;
  font-size: 12px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

@media (max-width: 768px) {
  .products-container {
    padding: 16px;
  }
  
  .search-card .el-row .el-col {
    margin-bottom: 12px;
  }
}
</style>
