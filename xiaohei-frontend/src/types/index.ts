// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  phone?: string
  avatar_url?: string
  role: 'user' | 'admin'
  is_active: boolean
  created_at: string
}

export interface UserLogin {
  username: string
  password: string
}

export interface UserRegister {
  username: string
  email: string
  password: string
  full_name?: string
  phone?: string
}

export interface UserUpdate {
  full_name?: string
  phone?: string
  avatar_url?: string
}

// 认证相关类型
export interface LoginResponse {
  access_token: string
  token_type: string
  user: User
}

// 商品相关类型
export interface Product {
  id: number
  title: string
  description?: string
  price: string
  category?: string
  image_url?: string
  is_available: boolean
  owner_id: number
  created_at: string
}

export interface ProductCreate {
  title: string
  description?: string
  price: number | string
  category?: string
  image_url?: string
}

export interface ProductUpdate {
  title?: string
  description?: string
  price?: number | string
  image_url?: string
  category?: string
  is_available?: boolean
}

// 消息相关类型
export interface Message {
  id: number
  title?: string
  content: string
  sender_id: number
  receiver_id?: number
  message_type: 'system' | 'user' | 'chat'
  is_read: boolean
  created_at: string
}

export interface MessageCreate {
  title?: string
  content: string
  receiver_id?: number
  message_type?: 'system' | 'user' | 'chat'
}

// 交易相关类型
export interface Transaction {
  id: number
  product_id: number
  buyer_id: number
  seller_id: number
  amount: string
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
  notes?: string
  created_at: string
}

export interface TransactionCreate {
  product_id: number
  amount: number | string
  notes?: string
}

export interface TransactionUpdate {
  status?: 'pending' | 'confirmed' | 'completed' | 'cancelled'
  notes?: string
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

export interface ListResponse<T = any> extends ApiResponse<T[]> {
  total?: number
  page?: number
  page_size?: number
}

// 聊天相关类型
export interface ChatMessage {
  id?: number
  content: string
  sender_id: number
  receiver_id: number
  timestamp: string
  type: 'text' | 'image' | 'file'
}

export interface OnlineUser {
  user_id: number
  username: string
  avatar_url?: string
  last_seen: string
}
