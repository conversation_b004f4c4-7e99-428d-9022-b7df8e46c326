import { get, post, put } from './index'
import type { 
  Message, 
  MessageCreate,
  OnlineUser,
  ApiResponse,
  ListResponse
} from '@/types'

// 消息相关 API
export const getMessageList = (params?: {
  skip?: number
  limit?: number
  message_type?: string
}): Promise<ListResponse<Message>> => {
  return get('/api/v1/messages/', params)
}

export const createMessage = (data: MessageCreate): Promise<ApiResponse<Message>> => {
  return post('/api/v1/messages/', data)
}

export const getMessageById = (messageId: number): Promise<ApiResponse<Message>> => {
  return get(`/api/v1/messages/${messageId}`)
}

export const markMessageRead = (messageId: number): Promise<ApiResponse<void>> => {
  return put(`/api/v1/messages/${messageId}/read`)
}

// 会话相关 API
export const getConversations = (): Promise<ApiResponse<any[]>> => {
  return get('/api/v1/messages/conversations')
}

export const getConversationMessages = (partnerId: number, params?: {
  skip?: number
  limit?: number
}): Promise<ApiResponse<Message[]>> => {
  return get(`/api/v1/messages/conversation/${partnerId}`, params)
}

// 聊天相关 API
export const getOnlineUsers = (): Promise<ApiResponse<{ online_users: OnlineUser[] }>> => {
  return get('/api/v1/chat/online-users')
}

export const checkUserStatus = (userId: number): Promise<ApiResponse<{ is_online: boolean }>> => {
  return get(`/api/v1/chat/user-status/${userId}`)
}

// WebSocket 聊天类
export class ChatWebSocket {
  private ws: WebSocket | null = null
  private url: string
  private token: string
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000

  constructor(token: string) {
    this.url = 'ws://************:8000/api/v1/chat/ws'
    this.token = token
  }

  connect(onMessage?: (data: any) => void, onError?: (error: Event) => void) {
    try {
      this.ws = new WebSocket(`${this.url}?token=${this.token}`)
      
      this.ws.onopen = () => {
        console.log('WebSocket 连接已建立')
        this.reconnectAttempts = 0
      }
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          onMessage?.(data)
        } catch (error) {
          console.error('解析消息失败:', error)
        }
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket 错误:', error)
        onError?.(error)
      }
      
      this.ws.onclose = () => {
        console.log('WebSocket 连接已关闭')
        this.attemptReconnect(onMessage, onError)
      }
    } catch (error) {
      console.error('WebSocket 连接失败:', error)
      onError?.(error as Event)
    }
  }

  private attemptReconnect(onMessage?: (data: any) => void, onError?: (error: Event) => void) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
      setTimeout(() => {
        this.connect(onMessage, onError)
      }, this.reconnectInterval)
    } else {
      console.error('WebSocket 重连失败，已达到最大重连次数')
    }
  }

  sendMessage(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.error('WebSocket 未连接')
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }
}
