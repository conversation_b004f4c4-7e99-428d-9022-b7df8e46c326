import { post, get, put } from './index'
import type { 
  UserLogin, 
  UserRegister, 
  UserUpdate, 
  LoginResponse, 
  User,
  ApiResponse,
  ListResponse
} from '@/types'

// 用户注册
export const registerUser = (data: UserRegister): Promise<ApiResponse<User>> => {
  return post('/api/v1/users/register', data)
}

// 用户登录
export const loginUser = (data: UserLogin): Promise<ApiResponse<LoginResponse>> => {
  return post('/api/v1/users/login', data)
}

// 获取当前用户信息
export const getCurrentUser = (): Promise<ApiResponse<User>> => {
  return get('/api/v1/users/me')
}

// 更新当前用户信息
export const updateCurrentUser = (data: UserUpdate): Promise<ApiResponse<User>> => {
  return put('/api/v1/users/me', data)
}

// 获取用户列表
export const getUserList = (params?: {
  skip?: number
  limit?: number
}): Promise<ListResponse<User>> => {
  return get('/api/v1/users/', params)
}

// 获取指定用户信息
export const getUserById = (userId: number): Promise<ApiResponse<User>> => {
  return get(`/api/v1/users/${userId}`)
}
