import { get, post, put, del } from './index'
import type { 
  Product, 
  ProductCreate, 
  ProductUpdate,
  ApiResponse,
  ListResponse
} from '@/types'

// 获取商品列表
export const getProductList = (params?: {
  skip?: number
  limit?: number
  category?: string
  search?: string
  available_only?: boolean
}): Promise<ListResponse<Product>> => {
  return get('/api/v1/products/', params)
}

// 创建商品
export const createProduct = (data: ProductCreate): Promise<ApiResponse<Product>> => {
  return post('/api/v1/products/', data)
}

// 获取我的商品列表
export const getMyProducts = (params?: {
  skip?: number
  limit?: number
}): Promise<ListResponse<Product>> => {
  return get('/api/v1/products/my', params)
}

// 获取商品分类列表
export const getCategories = (): Promise<ApiResponse<string[]>> => {
  return get('/api/v1/products/categories')
}

// 获取商品详情
export const getProductById = (productId: number): Promise<ApiResponse<Product>> => {
  return get(`/api/v1/products/${productId}`)
}

// 更新商品信息
export const updateProduct = (productId: number, data: ProductUpdate): Promise<ApiResponse<Product>> => {
  return put(`/api/v1/products/${productId}`, data)
}

// 删除商品
export const deleteProduct = (productId: number): Promise<ApiResponse<void>> => {
  return del(`/api/v1/products/${productId}`)
}
