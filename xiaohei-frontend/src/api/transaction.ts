import { get, post, put, del } from './index'
import type { 
  Transaction, 
  TransactionCreate, 
  TransactionUpdate,
  ApiResponse,
  ListResponse
} from '@/types'

// 获取交易列表
export const getTransactionList = (params?: {
  skip?: number
  limit?: number
  status?: string
  as_buyer?: boolean
  as_seller?: boolean
}): Promise<ListResponse<Transaction>> => {
  return get('/api/v1/transactions/', params)
}

// 创建交易
export const createTransaction = (data: TransactionCreate): Promise<ApiResponse<Transaction>> => {
  return post('/api/v1/transactions/', data)
}

// 获取作为买家的交易列表
export const getBuyerTransactions = (params?: {
  skip?: number
  limit?: number
}): Promise<ListResponse<Transaction>> => {
  return get('/api/v1/transactions/as-buyer', params)
}

// 获取作为卖家的交易列表
export const getSellerTransactions = (params?: {
  skip?: number
  limit?: number
}): Promise<ListResponse<Transaction>> => {
  return get('/api/v1/transactions/as-seller', params)
}

// 获取交易详情
export const getTransactionById = (transactionId: number): Promise<ApiResponse<Transaction>> => {
  return get(`/api/v1/transactions/${transactionId}`)
}

// 更新交易状态
export const updateTransaction = (transactionId: number, data: TransactionUpdate): Promise<ApiResponse<Transaction>> => {
  return put(`/api/v1/transactions/${transactionId}`, data)
}

// 取消交易
export const cancelTransaction = (transactionId: number): Promise<ApiResponse<void>> => {
  return del(`/api/v1/transactions/${transactionId}`)
}
