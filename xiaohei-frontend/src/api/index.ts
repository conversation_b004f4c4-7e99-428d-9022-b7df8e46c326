import axios from 'axios'
import type { AxiosInstance, AxiosResponse } from 'axios'

// API 基础配置
const API_BASE_URL = 'http://45.137.11.50:8000'

// 创建 axios 实例
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证 token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理统一响应格式
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 后端返回的标准格式: { code, message, data, success }
    const { data } = response
    if (data.success === false) {
      // 业务错误
      throw new Error(data.message || '请求失败')
    }
    return data
  },
  (error) => {
    // HTTP 错误
    if (error.response?.status === 401) {
      // 未授权，清除 token 并跳转到登录页
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_info')
      window.location.href = '/login'
    }
    
    const message = error.response?.data?.message || error.message || '网络错误'
    throw new Error(message)
  }
)

export default api

// 导出常用的 HTTP 方法
export const get = (url: string, params?: any) => api.get(url, { params })
export const post = (url: string, data?: any) => api.post(url, data)
export const put = (url: string, data?: any) => api.put(url, data)
export const del = (url: string) => api.delete(url)
