import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { OnlineUser, ChatMessage } from '@/types'
import { ChatWebSocket, getOnlineUsers } from '@/api/chat'
import { useUserStore } from './user'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const onlineUsers = ref<OnlineUser[]>([])
  const messages = ref<ChatMessage[]>([])
  const currentChatUser = ref<OnlineUser | null>(null)
  const ws = ref<ChatWebSocket | null>(null)
  const isConnected = ref(false)
  const loading = ref(false)

  // 计算属性
  const onlineUserCount = computed(() => onlineUsers.value.length)
  const currentChatMessages = computed(() => {
    if (!currentChatUser.value) return []
    return messages.value.filter(msg => 
      (msg.sender_id === currentChatUser.value!.user_id) ||
      (msg.receiver_id === currentChatUser.value!.user_id)
    )
  })

  // 连接 WebSocket
  const connectWebSocket = () => {
    const userStore = useUserStore()
    if (!userStore.token) return

    ws.value = new ChatWebSocket(userStore.token)
    
    ws.value.connect(
      // 消息处理
      (data) => {
        handleWebSocketMessage(data)
      },
      // 错误处理
      (error) => {
        console.error('WebSocket 连接错误:', error)
        isConnected.value = false
      }
    )
  }

  // 处理 WebSocket 消息
  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'connection_established':
        isConnected.value = true
        console.log('WebSocket 连接已建立')
        break
        
      case 'user_joined':
        // 用户上线
        if (!onlineUsers.value.find(u => u.user_id === data.user.user_id)) {
          onlineUsers.value.push(data.user)
        }
        break
        
      case 'user_left':
        // 用户下线
        onlineUsers.value = onlineUsers.value.filter(u => u.user_id !== data.user_id)
        break
        
      case 'chat_message':
        // 收到聊天消息
        messages.value.push({
          id: data.message.id,
          content: data.message.content,
          sender_id: data.message.sender_id,
          receiver_id: data.message.receiver_id,
          timestamp: data.message.timestamp || new Date().toISOString(),
          type: 'text'
        })
        break
        
      case 'online_users':
        // 在线用户列表更新
        onlineUsers.value = data.users || []
        break
        
      default:
        console.log('未知消息类型:', data)
    }
  }

  // 发送聊天消息
  const sendMessage = (content: string, receiverId: number) => {
    if (!ws.value || !ws.value.isConnected()) {
      throw new Error('WebSocket 未连接')
    }

    const message = {
      type: 'chat_message',
      content,
      receiver_id: receiverId,
      timestamp: new Date().toISOString()
    }

    ws.value.sendMessage(message)
    
    // 添加到本地消息列表
    const userStore = useUserStore()
    messages.value.push({
      content,
      sender_id: userStore.user?.id || 0,
      receiver_id: receiverId,
      timestamp: message.timestamp,
      type: 'text'
    })
  }

  // 获取在线用户列表
  const fetchOnlineUsers = async () => {
    loading.value = true
    try {
      const response = await getOnlineUsers()
      onlineUsers.value = response.data.online_users || []
      return response
    } catch (error) {
      console.error('获取在线用户失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 设置当前聊天用户
  const setCurrentChatUser = (user: OnlineUser | null) => {
    currentChatUser.value = user
  }

  // 断开 WebSocket 连接
  const disconnectWebSocket = () => {
    if (ws.value) {
      ws.value.disconnect()
      ws.value = null
    }
    isConnected.value = false
  }

  // 清空聊天数据
  const clearChatData = () => {
    onlineUsers.value = []
    messages.value = []
    currentChatUser.value = null
    disconnectWebSocket()
  }

  return {
    // 状态
    onlineUsers,
    messages,
    currentChatUser,
    isConnected,
    loading,
    
    // 计算属性
    onlineUserCount,
    currentChatMessages,
    
    // 方法
    connectWebSocket,
    sendMessage,
    fetchOnlineUsers,
    setCurrentChatUser,
    disconnectWebSocket,
    clearChatData
  }
})
