import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, UserLogin, UserRegister, UserUpdate } from '@/types'
import { loginUser, registerUser, getCurrentUser, updateCurrentUser } from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('access_token'))
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 登录
  const login = async (loginData: UserLogin) => {
    loading.value = true
    try {
      const response = await loginUser(loginData)
      const { access_token, user: userData } = response.data
      
      // 保存 token 和用户信息
      token.value = access_token
      user.value = userData
      localStorage.setItem('access_token', access_token)
      localStorage.setItem('user_info', JSON.stringify(userData))
      
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData: UserRegister) => {
    loading.value = true
    try {
      const response = await registerUser(registerData)
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('access_token')
    localStorage.removeItem('user_info')
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!token.value) return
    
    loading.value = true
    try {
      const response = await getCurrentUser()
      user.value = response.data
      localStorage.setItem('user_info', JSON.stringify(response.data))
      return response
    } catch (error) {
      // 如果获取用户信息失败，可能是 token 过期
      logout()
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新用户信息
  const updateUser = async (updateData: UserUpdate) => {
    loading.value = true
    try {
      const response = await updateCurrentUser(updateData)
      user.value = response.data
      localStorage.setItem('user_info', JSON.stringify(response.data))
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 初始化用户信息（从 localStorage 恢复）
  const initUser = () => {
    const savedUser = localStorage.getItem('user_info')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  return {
    // 状态
    user,
    token,
    loading,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    
    // 方法
    login,
    register,
    logout,
    fetchCurrentUser,
    updateUser,
    initUser
  }
})
