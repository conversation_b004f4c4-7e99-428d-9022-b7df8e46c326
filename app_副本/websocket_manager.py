from typing import Dict, List
from fastapi import WebSocket, WebSocketDisconnect
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket 连接管理器"""
    
    def __init__(self):
        # 存储活跃连接：{user_id: websocket}
        self.active_connections: Dict[int, WebSocket] = {}
        # 存储用户信息：{user_id: user_info}
        self.user_info: Dict[int, dict] = {}
    
    async def connect(self, websocket: WebSocket, user_id: int, user_info: dict):
        """接受 WebSocket 连接"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_info[user_id] = user_info
        logger.info(f"用户 {user_id} ({user_info.get('username')}) 已连接")
        
        # 通知其他用户有新用户上线
        await self.broadcast_user_status(user_id, "online")
    
    def disconnect(self, user_id: int):
        """断开连接"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_info:
            del self.user_info[user_id]
        logger.info(f"用户 {user_id} 已断开连接")
    
    async def send_personal_message(self, message: str, user_id: int):
        """发送个人消息"""
        if user_id in self.active_connections:
            websocket = self.active_connections[user_id]
            try:
                await websocket.send_text(message)
                return True
            except Exception as e:
                logger.error(f"发送消息给用户 {user_id} 失败: {e}")
                self.disconnect(user_id)
                return False
        return False
    
    async def send_message_to_user(self, sender_id: int, receiver_id: int, message: dict):
        """发送消息给指定用户"""
        message_data = {
            "type": "private_message",
            "sender_id": sender_id,
            "sender_name": self.user_info.get(sender_id, {}).get("username", "未知用户"),
            "receiver_id": receiver_id,
            "message": message.get("content", ""),
            "timestamp": datetime.now().isoformat()
        }
        
        # 发送给接收者
        success = await self.send_personal_message(json.dumps(message_data), receiver_id)
        
        # 也发送给发送者（确认消息已发送）
        message_data["type"] = "message_sent"
        await self.send_personal_message(json.dumps(message_data), sender_id)
        
        return success
    
    async def broadcast_user_status(self, user_id: int, status: str):
        """广播用户状态变化"""
        user_info = self.user_info.get(user_id, {})
        status_message = {
            "type": "user_status",
            "user_id": user_id,
            "username": user_info.get("username", "未知用户"),
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
        
        # 广播给所有在线用户（除了状态变化的用户本身）
        for connected_user_id, websocket in self.active_connections.items():
            if connected_user_id != user_id:
                try:
                    await websocket.send_text(json.dumps(status_message))
                except Exception as e:
                    logger.error(f"广播状态给用户 {connected_user_id} 失败: {e}")
                    self.disconnect(connected_user_id)
    
    async def get_online_users(self, requesting_user_id: int):
        """获取在线用户列表"""
        online_users = []
        for user_id, user_info in self.user_info.items():
            if user_id != requesting_user_id:  # 不包括请求者自己
                online_users.append({
                    "user_id": user_id,
                    "username": user_info.get("username"),
                    "full_name": user_info.get("full_name"),
                    "avatar_url": user_info.get("avatar_url")
                })
        
        response = {
            "type": "online_users",
            "users": online_users,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.send_personal_message(json.dumps(response), requesting_user_id)
    
    def is_user_online(self, user_id: int) -> bool:
        """检查用户是否在线"""
        return user_id in self.active_connections


# 全局连接管理器实例
manager = ConnectionManager()
