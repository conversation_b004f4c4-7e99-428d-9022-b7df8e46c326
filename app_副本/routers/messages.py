from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from typing import Optional
from app.database import get_db
from app import models, schemas, auth
from app.response_utils import (
    success_response, error_response, create_response, update_response,
    list_response, not_found_response, forbidden_response,
    StatusCode, ResponseMessage
)

router = APIRouter()


@router.post("/")
async def create_message(
    message: schemas.MessageCreate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建消息"""
    # 如果有接收者，检查接收者是否存在
    if message.receiver_id:
        receiver = db.query(models.User).filter(models.User.id == message.receiver_id).first()
        if not receiver:
            return not_found_response(message="接收者不存在")

    db_message = models.Message(
        sender_id=current_user.id,
        receiver_id=message.receiver_id,
        title=message.title,
        content=message.content,
        message_type=message.message_type
    )
    db.add(db_message)
    db.commit()
    db.refresh(db_message)

    # 格式化消息数据，包含发送方和接收方信息
    message_data = {
        "id": db_message.id,
        "title": db_message.title,
        "content": db_message.content,
        "message_type": db_message.message_type.value,
        "is_read": db_message.is_read,
        "created_at": db_message.created_at.strftime("%Y-%m-%d %H:%M:%S"),
        "sender_id": db_message.sender_id,
        "sender_username": current_user.username,
        "sender_full_name": current_user.full_name,
        "sender_avatar_url": current_user.avatar_url,
        "receiver_id": db_message.receiver_id,
        "receiver_username": receiver.username if receiver else None,
        "receiver_full_name": receiver.full_name if receiver else None,
        "receiver_avatar_url": receiver.avatar_url if receiver else None
    }

    return create_response(data=message_data, message="消息创建成功")


@router.get("/")
async def list_messages(
    skip: int = 0,
    limit: int = 100,
    message_type: Optional[str] = None,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取消息列表"""
    query = db.query(models.Message).filter(
        or_(
            models.Message.receiver_id == current_user.id,
            models.Message.sender_id == current_user.id
        )
    )

    if message_type:
        query = query.filter(models.Message.message_type == message_type)

    messages = query.order_by(models.Message.created_at.desc()).offset(skip).limit(limit).all()

    # 格式化消息数据，包含发送方和接收方信息
    messages_data = []
    for message in messages:
        message_data = {
            "id": message.id,
            "title": message.title,
            "content": message.content,
            "message_type": message.message_type.value,
            "is_read": message.is_read,
            "created_at": message.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "sender_id": message.sender_id,
            "sender_username": message.sender.username if message.sender else None,
            "sender_full_name": message.sender.full_name if message.sender else None,
            "sender_avatar_url": message.sender.avatar_url if message.sender else None,
            "receiver_id": message.receiver_id,
            "receiver_username": message.receiver.username if message.receiver else None,
            "receiver_full_name": message.receiver.full_name if message.receiver else None,
            "receiver_avatar_url": message.receiver.avatar_url if message.receiver else None
        }
        messages_data.append(message_data)

    return success_response(data=messages_data, message="获取消息列表成功")


@router.get("/conversations")
async def get_conversations(
    user_id: Optional[int] = Query(None, description="指定用户ID，只获取与该用户的对话记录"),
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取会话列表"""
    # 构建查询条件
    query_filter = and_(
        models.Message.message_type == models.MessageType.CHAT,
        or_(
            models.Message.receiver_id == current_user.id,
            models.Message.sender_id == current_user.id
        )
    )

    # 如果指定了user_id，只获取与该用户的对话
    if user_id:
        query_filter = and_(
            query_filter,
            or_(
                and_(models.Message.sender_id == current_user.id, models.Message.receiver_id == user_id),
                and_(models.Message.sender_id == user_id, models.Message.receiver_id == current_user.id)
            )
        )

    # 获取消息
    messages = db.query(models.Message).filter(query_filter).order_by(models.Message.created_at.desc()).all()

    # 按对话伙伴分组
    conversations = {}
    for message in messages:
        # 确定对话伙伴
        partner_id = message.receiver_id if message.sender_id == current_user.id else message.sender_id

        if partner_id not in conversations:
            # 获取伙伴信息
            partner = db.query(models.User).filter(models.User.id == partner_id).first()
            conversations[partner_id] = {
                "partner_id": partner_id,
                "partner_username": partner.username if partner else "未知用户",
                "partner_full_name": partner.full_name if partner else "未知用户",
                "partner_avatar_url": partner.avatar_url if partner else None,
                "last_message": message.content,
                "last_message_time": message.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "unread_count": 0,
                # 添加发送方和接收方信息
                "sender_id": message.sender_id,
                "sender_username": message.sender.username if message.sender else None,
                "sender_full_name": message.sender.full_name if message.sender else None,
                "sender_avatar_url": message.sender.avatar_url if message.sender else None,
                "receiver_id": message.receiver_id,
                "receiver_username": message.receiver.username if message.receiver else None,
                "receiver_full_name": message.receiver.full_name if message.receiver else None,
                "receiver_avatar_url": message.receiver.avatar_url if message.receiver else None
            }

        # 计算未读消息数（只计算接收到的消息）
        if message.receiver_id == current_user.id and not message.is_read:
            conversations[partner_id]["unread_count"] += 1

    return success_response(data=list(conversations.values()), message="获取会话列表成功")


@router.get("/conversation/{partner_id}")
async def get_conversation_messages(
    partner_id: int,
    skip: int = 0,
    limit: int = 50,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取与指定用户的聊天记录"""
    messages = db.query(models.Message).filter(
        and_(
            models.Message.message_type == models.MessageType.CHAT,
            or_(
                and_(
                    models.Message.sender_id == current_user.id,
                    models.Message.receiver_id == partner_id
                ),
                and_(
                    models.Message.sender_id == partner_id,
                    models.Message.receiver_id == current_user.id
                )
            )
        )
    ).order_by(models.Message.created_at.desc()).offset(skip).limit(limit).all()

    # 标记接收到的消息为已读
    unread_messages = db.query(models.Message).filter(
        and_(
            models.Message.sender_id == partner_id,
            models.Message.receiver_id == current_user.id,
            models.Message.is_read == False
        )
    ).all()

    for message in unread_messages:
        message.is_read = True

    db.commit()

    # 格式化消息数据，包含发送方和接收方信息
    messages_data = []
    for message in messages:
        message_data = {
            "id": message.id,
            "title": message.title,
            "content": message.content,
            "message_type": message.message_type.value,
            "is_read": message.is_read,
            "created_at": message.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "sender_id": message.sender_id,
            "sender_username": message.sender.username if message.sender else None,
            "sender_full_name": message.sender.full_name if message.sender else None,
            "sender_avatar_url": message.sender.avatar_url if message.sender else None,
            "receiver_id": message.receiver_id,
            "receiver_username": message.receiver.username if message.receiver else None,
            "receiver_full_name": message.receiver.full_name if message.receiver else None,
            "receiver_avatar_url": message.receiver.avatar_url if message.receiver else None
        }
        messages_data.append(message_data)

    return success_response(data=messages_data[::-1], message="获取聊天记录成功")  # 返回正序消息


@router.get("/{message_id}")
async def get_message(
    message_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取消息详情"""
    message = db.query(models.Message).filter(models.Message.id == message_id).first()
    if not message:
        return not_found_response(message="消息不存在")

    # 检查权限
    if message.sender_id != current_user.id and message.receiver_id != current_user.id:
        return forbidden_response(message="无权访问此消息")

    # 如果是接收者查看消息，标记为已读
    if message.receiver_id == current_user.id and not message.is_read:
        message.is_read = True
        db.commit()

    # 格式化消息数据，包含发送方和接收方信息
    message_data = {
        "id": message.id,
        "title": message.title,
        "content": message.content,
        "message_type": message.message_type.value,
        "is_read": message.is_read,
        "created_at": message.created_at.strftime("%Y-%m-%d %H:%M:%S"),
        "sender_id": message.sender_id,
        "sender_username": message.sender.username if message.sender else None,
        "sender_full_name": message.sender.full_name if message.sender else None,
        "sender_avatar_url": message.sender.avatar_url if message.sender else None,
        "receiver_id": message.receiver_id,
        "receiver_username": message.receiver.username if message.receiver else None,
        "receiver_full_name": message.receiver.full_name if message.receiver else None,
        "receiver_avatar_url": message.receiver.avatar_url if message.receiver else None
    }

    return success_response(data=message_data, message="获取消息详情成功")


@router.put("/{message_id}/read")
async def mark_message_read(
    message_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """标记消息为已读"""
    message = db.query(models.Message).filter(models.Message.id == message_id).first()
    if not message:
        return not_found_response(message="消息不存在")

    # 只有接收者可以标记消息为已读
    if message.receiver_id != current_user.id:
        return forbidden_response(message="无权操作此消息")

    message.is_read = True
    db.commit()

    return update_response(message="消息已标记为已读")
