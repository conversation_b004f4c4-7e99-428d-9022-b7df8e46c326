from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app import models, schemas, auth
from app.response_utils import (
    success_response, error_response, create_response, update_response,
    list_response, not_found_response, forbidden_response,
    StatusCode, ResponseMessage
)

router = APIRouter()


@router.post("/")
async def create_product(
    product: schemas.ProductCreate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建商品"""
    db_product = models.Product(
        title=product.title,
        description=product.description,
        price=product.price,
        image_url=product.image_url,
        category=product.category,
        owner_id=current_user.id
    )
    db.add(db_product)
    db.commit()
    db.refresh(db_product)

    product_data = {
        "id": db_product.id,
        "title": db_product.title,
        "description": db_product.description,
        "price": float(db_product.price),
        "image_url": db_product.image_url,
        "category": db_product.category,
        "is_available": db_product.is_available,
        "owner_id": db_product.owner_id,
        "created_at": db_product.created_at.isoformat()
    }

    return create_response(data=product_data, message=ResponseMessage.PRODUCT_CREATED)


@router.get("/")
async def list_products(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    search: Optional[str] = None,
    available_only: bool = True,
    db: Session = Depends(get_db)
):
    """获取商品列表"""
    query = db.query(models.Product)

    if available_only:
        query = query.filter(models.Product.is_available == True)

    if category:
        query = query.filter(models.Product.category == category)

    if search:
        query = query.filter(
            models.Product.title.contains(search) |
            models.Product.description.contains(search)
        )

    # 获取总数
    total = query.count()

    # 获取商品列表
    products = query.order_by(models.Product.created_at.desc()).offset(skip).limit(limit).all()

    # 转换为字典格式
    products_data = []
    for product in products:
        products_data.append({
            "id": product.id,
            "title": product.title,
            "description": product.description,
            "price": float(product.price),
            "image_url": product.image_url,
            "category": product.category,
            "is_available": product.is_available,
            "owner_id": product.owner_id,
            "created_at": product.created_at.isoformat()
        })

    return list_response(
        data=products_data,
        total=total,
        page=skip // limit + 1 if limit > 0 else 1,
        page_size=limit,
        message="获取商品列表成功"
    )


@router.get("/my", response_model=List[schemas.Product])
async def list_my_products(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取我的商品列表"""
    products = db.query(models.Product).filter(
        models.Product.owner_id == current_user.id
    ).order_by(models.Product.created_at.desc()).offset(skip).limit(limit).all()
    
    return products


@router.get("/categories")
async def get_categories(db: Session = Depends(get_db)):
    """获取商品分类列表"""
    categories = db.query(models.Product.category).distinct().all()
    return {"categories": [cat[0] for cat in categories if cat[0]]}


@router.get("/{product_id}", response_model=schemas.Product)
async def get_product(product_id: int, db: Session = Depends(get_db)):
    """获取商品详情"""
    product = db.query(models.Product).filter(models.Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="商品不存在")
    
    return product


@router.put("/{product_id}", response_model=schemas.Product)
async def update_product(
    product_id: int,
    product_update: schemas.ProductUpdate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新商品信息"""
    product = db.query(models.Product).filter(models.Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="商品不存在")
    
    # 检查权限
    if product.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权修改此商品")
    
    # 更新字段
    for field, value in product_update.dict(exclude_unset=True).items():
        setattr(product, field, value)
    
    db.commit()
    db.refresh(product)
    
    return product


@router.delete("/{product_id}")
async def delete_product(
    product_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除商品"""
    product = db.query(models.Product).filter(models.Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="商品不存在")
    
    # 检查权限
    if product.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权删除此商品")
    
    # 检查是否有进行中的交易
    active_transactions = db.query(models.Transaction).filter(
        models.Transaction.product_id == product_id,
        models.Transaction.status.in_([models.TransactionStatus.PENDING, models.TransactionStatus.CONFIRMED])
    ).first()
    
    if active_transactions:
        raise HTTPException(status_code=400, detail="商品有进行中的交易，无法删除")
    
    db.delete(product)
    db.commit()
    
    return {"message": "商品删除成功"}
