"""
响应工具模块 - 统一API响应格式
"""
from typing import Any, Optional, List, Dict
from fastapi import HTTPException
from fastapi.responses import JSONResponse


def success_response(
    data: Any = None,
    message: str = "操作成功",
    code: int = 200
) -> Dict[str, Any]:
    """
    成功响应
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 状态码
    
    Returns:
        标准响应格式
    """
    return {
        "code": code,
        "message": message,
        "data": data,
        "success": True
    }


def error_response(
    message: str = "操作失败",
    code: int = 400,
    data: Any = None
) -> Dict[str, Any]:
    """
    错误响应
    
    Args:
        message: 错误消息
        code: 错误码
        data: 错误详情数据
    
    Returns:
        标准错误响应格式
    """
    return {
        "code": code,
        "message": message,
        "data": data,
        "success": False
    }


def list_response(
    data: List[Any],
    total: Optional[int] = None,
    page: Optional[int] = None,
    page_size: Optional[int] = None,
    message: str = "获取成功",
    code: int = 200
) -> Dict[str, Any]:
    """
    列表响应
    
    Args:
        data: 列表数据
        total: 总数
        page: 当前页
        page_size: 每页大小
        message: 响应消息
        code: 状态码
    
    Returns:
        标准列表响应格式
    """
    response = {
        "code": code,
        "message": message,
        "data": data,
        "success": True
    }
    
    # 添加分页信息
    if total is not None:
        response["total"] = total
    if page is not None:
        response["page"] = page
    if page_size is not None:
        response["page_size"] = page_size
    
    return response


def create_response(
    data: Any = None,
    message: str = "创建成功",
    code: int = 201
) -> Dict[str, Any]:
    """创建成功响应"""
    return success_response(data=data, message=message, code=code)


def update_response(
    data: Any = None,
    message: str = "更新成功",
    code: int = 200
) -> Dict[str, Any]:
    """更新成功响应"""
    return success_response(data=data, message=message, code=code)


def delete_response(
    message: str = "删除成功",
    code: int = 200
) -> Dict[str, Any]:
    """删除成功响应"""
    return success_response(data=None, message=message, code=code)


def not_found_response(
    message: str = "资源不存在",
    code: int = 404
) -> JSONResponse:
    """404响应"""
    return JSONResponse(
        status_code=404,
        content=error_response(message=message, code=code)
    )


def forbidden_response(
    message: str = "权限不足",
    code: int = 403
) -> JSONResponse:
    """403响应"""
    return JSONResponse(
        status_code=403,
        content=error_response(message=message, code=code)
    )


def unauthorized_response(
    message: str = "未授权访问",
    code: int = 401
) -> JSONResponse:
    """401响应"""
    return JSONResponse(
        status_code=401,
        content=error_response(message=message, code=code)
    )


def validation_error_response(
    message: str = "参数验证失败",
    code: int = 422,
    errors: Any = None
) -> JSONResponse:
    """参数验证错误响应"""
    return JSONResponse(
        status_code=422,
        content=error_response(message=message, code=code, data=errors)
    )


def server_error_response(
    message: str = "服务器内部错误",
    code: int = 500
) -> JSONResponse:
    """500响应"""
    return JSONResponse(
        status_code=500,
        content=error_response(message=message, code=code)
    )


# 常用状态码常量
class StatusCode:
    """HTTP状态码常量"""
    OK = 200
    CREATED = 201
    NO_CONTENT = 204
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    INTERNAL_SERVER_ERROR = 500


# 常用响应消息常量
class ResponseMessage:
    """响应消息常量"""
    SUCCESS = "操作成功"
    CREATED = "创建成功"
    UPDATED = "更新成功"
    DELETED = "删除成功"
    NOT_FOUND = "资源不存在"
    FORBIDDEN = "权限不足"
    UNAUTHORIZED = "未授权访问"
    VALIDATION_ERROR = "参数验证失败"
    SERVER_ERROR = "服务器内部错误"
    
    # 用户相关
    USER_NOT_FOUND = "用户不存在"
    USER_CREATED = "用户注册成功"
    LOGIN_SUCCESS = "登录成功"
    LOGIN_FAILED = "用户名或密码错误"
    
    # 商品相关
    PRODUCT_NOT_FOUND = "商品不存在"
    PRODUCT_CREATED = "商品发布成功"
    PRODUCT_UPDATED = "商品更新成功"
    PRODUCT_DELETED = "商品删除成功"
    
    # 交易相关
    TRANSACTION_NOT_FOUND = "交易不存在"
    TRANSACTION_CREATED = "交易创建成功"
    TRANSACTION_UPDATED = "交易状态更新成功"
    
    # 消息相关
    MESSAGE_NOT_FOUND = "消息不存在"
    MESSAGE_SENT = "消息发送成功"
    MESSAGE_READ = "消息已读"
