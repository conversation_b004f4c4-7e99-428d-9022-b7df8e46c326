#!/usr/bin/env python3
"""
将商品系统迁移为礼品卡回收系统
"""

from sqlalchemy import create_engine, text
from app.config import settings

def migrate_products_system():
    """迁移商品系统为礼品卡回收系统"""
    
    # 创建数据库连接
    engine = create_engine(settings.database_url, echo=True)
    
    try:
        with engine.connect() as connection:
            print("🔄 开始迁移商品系统为礼品卡回收系统...")
            
            # 1. 备份现有商品表
            print("1. 备份现有商品表...")
            try:
                connection.execute(text("""
                    CREATE TABLE products_backup AS SELECT * FROM products
                """))
                connection.commit()
                print("   ✓ 商品表备份成功")
            except Exception as e:
                print(f"   ⚠️ 备份失败（可能已存在）: {e}")
            
            # 2. 先删除外键约束
            print("2. 删除外键约束...")
            try:
                # 删除transactions表中的外键约束
                connection.execute(text("""
                    ALTER TABLE transactions DROP FOREIGN KEY transactions_ibfk_1
                """))
                connection.commit()
                print("   ✓ 外键约束删除成功")
            except Exception as e:
                print(f"   ⚠️ 删除外键约束失败（可能不存在）: {e}")

            # 3. 删除现有商品表
            print("3. 删除现有商品表...")
            try:
                connection.execute(text("DROP TABLE IF EXISTS products"))
                connection.commit()
                print("   ✓ 现有商品表删除成功")
            except Exception as e:
                print(f"   ✗ 删除商品表失败: {e}")
                connection.rollback()
                return False
            
            # 4. 创建新的礼品卡回收商品表
            print("4. 创建新的礼品卡回收商品表...")
            try:
                connection.execute(text("""
                    CREATE TABLE products (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(200) NOT NULL COMMENT '商品名称',
                        avatar_url VARCHAR(255) COMMENT '头像URL',
                        description TEXT COMMENT '商品描述',
                        min_amount DECIMAL(10,2) NOT NULL COMMENT '最小接收金额',
                        max_amount DECIMAL(10,2) NOT NULL COMMENT '最大接收金额',
                        currency ENUM('USD', 'NGN') DEFAULT 'USD' COMMENT '货币类型',
                        average_time_minutes INT DEFAULT 5 COMMENT '平均处理时间（分钟）',
                        transaction_count INT DEFAULT 0 COMMENT '交易量统计',
                        like_count INT DEFAULT 0 COMMENT '点赞数量',
                        success_rate DECIMAL(5,2) DEFAULT 100.00 COMMENT '成功率百分比',
                        is_available BOOLEAN DEFAULT TRUE COMMENT '是否可用',
                        category VARCHAR(50) COMMENT '分类',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """))
                connection.commit()
                print("   ✓ 新商品表创建成功")
            except Exception as e:
                print(f"   ✗ 创建新商品表失败: {e}")
                connection.rollback()
                return False
            
            # 5. 更新交易表，使product_id和buyer_id可为空
            print("5. 更新交易表结构...")
            try:
                connection.execute(text("""
                    ALTER TABLE transactions 
                    MODIFY COLUMN product_id INT NULL,
                    MODIFY COLUMN buyer_id INT NULL
                """))
                connection.commit()
                print("   ✓ 交易表结构更新成功")
            except Exception as e:
                print(f"   ✗ 更新交易表结构失败: {e}")
                connection.rollback()
                return False
            
            # 6. 重新创建外键约束
            print("6. 重新创建外键约束...")
            try:
                connection.execute(text("""
                    ALTER TABLE transactions
                    ADD CONSTRAINT fk_transactions_product_id
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL
                """))
                connection.commit()
                print("   ✓ 外键约束重新创建成功")
            except Exception as e:
                print(f"   ⚠️ 重新创建外键约束失败: {e}")

            # 7. 插入示例礼品卡回收商品
            print("7. 插入示例礼品卡回收商品...")
            try:
                sample_products = [
                    {
                        'name': 'DavidCard',
                        'description': '专业礼品卡回收服务，快速安全',
                        'min_amount': 10.00,
                        'max_amount': 500.00,
                        'currency': 'USD',
                        'category': 'Razer',
                        'average_time_minutes': 5,
                        'transaction_count': 127670,
                        'like_count': 11889,
                        'success_rate': 100.00
                    },
                    {
                        'name': 'SteamCard',
                        'description': 'Steam礼品卡专业回收',
                        'min_amount': 5.00,
                        'max_amount': 1000.00,
                        'currency': 'USD',
                        'category': 'Steam',
                        'average_time_minutes': 3,
                        'transaction_count': 89543,
                        'like_count': 8765,
                        'success_rate': 99.8
                    },
                    {
                        'name': 'AmazonCard',
                        'description': 'Amazon礼品卡快速回收',
                        'min_amount': 20.00,
                        'max_amount': 2000.00,
                        'currency': 'USD',
                        'category': 'Amazon',
                        'average_time_minutes': 7,
                        'transaction_count': 156789,
                        'like_count': 15432,
                        'success_rate': 99.9
                    }
                ]
                
                for product in sample_products:
                    connection.execute(text("""
                        INSERT INTO products (name, description, min_amount, max_amount, currency, 
                                            category, average_time_minutes, transaction_count, 
                                            like_count, success_rate)
                        VALUES (:name, :description, :min_amount, :max_amount, :currency,
                                :category, :average_time_minutes, :transaction_count,
                                :like_count, :success_rate)
                    """), product)
                
                connection.commit()
                print("   ✓ 示例商品插入成功")
            except Exception as e:
                print(f"   ✗ 插入示例商品失败: {e}")
                connection.rollback()
                return False
            
            print("🎉 礼品卡回收系统迁移完成！")
            
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        return False
        
    return True

if __name__ == "__main__":
    success = migrate_products_system()
    if success:
        print("✅ 礼品卡回收系统迁移成功！")
    else:
        print("❌ 礼品卡回收系统迁移失败！")
