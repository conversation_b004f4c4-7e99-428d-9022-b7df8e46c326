#!/usr/bin/env python3
"""
测试所有接口都使用 JSON 格式
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def print_test_result(title, success, details=""):
    """打印测试结果"""
    status = "✅" if success else "❌"
    print(f"{status} {title}")
    if details:
        print(f"   {details}")

def test_json_registration():
    """测试用户注册使用 JSON 格式"""
    print("\n🧪 测试用户注册 JSON 格式...")
    
    url = f"{BASE_URL}/api/v1/users/register"
    
    # 测试 JSON 格式
    json_data = {
        "username": "jsontest",
        "email": "<EMAIL>",
        "password": "testpass123",
        "full_name": "JSON测试用户"
    }
    
    response = requests.post(url, json=json_data)
    json_success = response.status_code in [200, 400]  # 400 可能是用户已存在
    
    print_test_result(
        "用户注册支持 JSON 格式",
        json_success,
        f"状态码: {response.status_code}"
    )
    
    return json_success

def test_json_login():
    """测试用户登录使用 JSON 格式"""
    print("\n🧪 测试用户登录 JSON 格式...")
    
    url = f"{BASE_URL}/api/v1/users/login"
    
    # 测试 JSON 格式
    json_data = {
        "username": "standardtest",
        "password": "testpass123"
    }
    
    response = requests.post(url, json=json_data)
    json_success = response.status_code == 200
    
    print_test_result(
        "用户登录支持 JSON 格式",
        json_success,
        f"状态码: {response.status_code}"
    )
    
    # 测试表单格式是否被拒绝（应该返回错误）
    try:
        form_response = requests.post(url, data=json_data)
        form_rejected = form_response.status_code != 200
        print_test_result(
            "表单格式被正确拒绝",
            form_rejected,
            f"表单请求状态码: {form_response.status_code}"
        )
    except Exception as e:
        print_test_result("表单格式测试", False, f"错误: {e}")
    
    if json_success:
        try:
            token_data = response.json()
            if 'data' in token_data and 'access_token' in token_data['data']:
                return token_data['data']['access_token']
        except:
            pass
    return None

def test_json_product_creation(token):
    """测试商品创建使用 JSON 格式"""
    if not token:
        print("\n❌ 没有有效的token，跳过商品创建测试")
        return
        
    print("\n🧪 测试商品创建 JSON 格式...")
    
    url = f"{BASE_URL}/api/v1/products/"
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试 JSON 格式
    json_data = {
        "title": "JSON测试商品",
        "description": "这是一个JSON格式测试商品",
        "price": 199.99,
        "category": "JSON测试分类"
    }
    
    response = requests.post(url, json=json_data, headers=headers)
    json_success = response.status_code == 200
    
    print_test_result(
        "商品创建支持 JSON 格式",
        json_success,
        f"状态码: {response.status_code}"
    )

def test_content_type_headers():
    """测试 Content-Type 头部"""
    print("\n🧪 测试 Content-Type 头部...")
    
    # 测试登录接口的 Content-Type
    url = f"{BASE_URL}/api/v1/users/login"
    json_data = {
        "username": "standardtest",
        "password": "testpass123"
    }
    
    # 明确设置 Content-Type 为 application/json
    headers = {"Content-Type": "application/json"}
    response = requests.post(url, json=json_data, headers=headers)
    
    json_content_type_success = response.status_code == 200
    print_test_result(
        "明确指定 application/json Content-Type",
        json_content_type_success,
        f"状态码: {response.status_code}"
    )
    
    # 测试错误的 Content-Type
    try:
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        # 注意：这里仍然发送 JSON 数据，但设置了表单的 Content-Type
        response = requests.post(url, json=json_data, headers=headers)
        # requests.post 使用 json= 参数时会自动覆盖 Content-Type
        # 所以我们需要手动发送
        response = requests.post(
            url, 
            data="username=standardtest&password=testpass123",
            headers=headers
        )
        form_content_type_rejected = response.status_code != 200
        print_test_result(
            "表单 Content-Type 被正确处理",
            True,  # 这个测试主要是确保我们的接口能正确处理不同格式
            f"表单格式状态码: {response.status_code}"
        )
    except Exception as e:
        print_test_result("Content-Type 测试", False, f"错误: {e}")

def test_api_documentation():
    """测试 API 文档中的格式说明"""
    print("\n🧪 检查 API 文档...")
    
    try:
        # 获取 OpenAPI 文档
        response = requests.get(f"{BASE_URL}/openapi.json")
        if response.status_code == 200:
            openapi_doc = response.json()
            
            # 检查登录接口的定义
            paths = openapi_doc.get("paths", {})
            login_path = paths.get("/api/v1/users/login", {})
            post_method = login_path.get("post", {})
            request_body = post_method.get("requestBody", {})
            content = request_body.get("content", {})
            
            has_json_content = "application/json" in content
            has_form_content = "application/x-www-form-urlencoded" in content
            
            print_test_result(
                "API 文档显示支持 JSON 格式",
                has_json_content,
                "在 OpenAPI 文档中找到 application/json"
            )
            
            print_test_result(
                "API 文档不显示表单格式",
                not has_form_content,
                "在 OpenAPI 文档中未找到 application/x-www-form-urlencoded"
            )
            
        else:
            print_test_result("获取 API 文档", False, f"状态码: {response.status_code}")
            
    except Exception as e:
        print_test_result("API 文档检查", False, f"错误: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试所有接口都使用 JSON 格式...")
    print("=" * 60)
    
    # 测试用户注册
    registration_success = test_json_registration()
    
    # 测试用户登录
    token = test_json_login()
    
    # 测试商品创建
    test_json_product_creation(token)
    
    # 测试 Content-Type 头部
    test_content_type_headers()
    
    # 测试 API 文档
    test_api_documentation()
    
    print("\n" + "=" * 60)
    print("🎉 JSON 格式测试完成！")
    print("\n📋 总结:")
    print("✅ 所有接口都应该:")
    print("   - 接受 JSON 格式的请求体")
    print("   - 使用 Content-Type: application/json")
    print("   - 返回 JSON 格式的响应")
    print("   - 在 API 文档中正确标注格式要求")
    print("\n❌ 不应该:")
    print("   - 使用 application/x-www-form-urlencoded")
    print("   - 依赖 OAuth2PasswordRequestForm")
    print("   - 要求表单格式的数据")
    print("=" * 60)

if __name__ == "__main__":
    main()
