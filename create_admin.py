#!/usr/bin/env python3
"""
创建管理员用户脚本
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.database import engine, get_db
from app import models, auth

def create_admin_user():
    """创建管理员用户"""
    try:
        print("🚀 开始创建管理员用户...")
        
        # 创建数据库会话
        db = Session(bind=engine)

        # 管理员用户信息
        admin_username = "admin"
        admin_email = "<EMAIL>"
        admin_password = "admin123"
        admin_full_name = "系统管理员"

        # 检查是否已存在管理员用户
        existing_admin = db.query(models.User).filter(
            models.User.role == models.UserRole.ADMIN
        ).first()

        if existing_admin:
            print(f"✅ 管理员用户已存在: {existing_admin.username}")
            print(f"   邮箱: {existing_admin.email}")
            print(f"   创建时间: {existing_admin.created_at}")
            print("🔄 重置管理员密码...")
            existing_admin.hashed_password = auth.get_password_hash(admin_password)
            db.commit()
            print(f"✅ 管理员密码已重置为: {admin_password}")
            return
        
        # 检查用户名是否已存在
        existing_user = db.query(models.User).filter(
            models.User.username == admin_username
        ).first()
        
        if existing_user:
            print(f"⚠️  用户名 '{admin_username}' 已存在，将其设置为管理员并重置密码...")
            existing_user.role = models.UserRole.ADMIN
            existing_user.hashed_password = auth.get_password_hash(admin_password)
            db.commit()
            db.refresh(existing_user)
            print(f"✅ 用户 '{admin_username}' 已设置为管理员，密码已重置为: {admin_password}")
            return
        
        # 创建管理员用户
        hashed_password = auth.get_password_hash(admin_password)
        admin_user = models.User(
            username=admin_username,
            email=admin_email,
            full_name=admin_full_name,
            hashed_password=hashed_password,
            role=models.UserRole.ADMIN,
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("✅ 管理员用户创建成功！")
        print("\n📋 管理员信息：")
        print(f"  用户名: {admin_username}")
        print(f"  邮箱: {admin_email}")
        print(f"  密码: {admin_password}")
        print(f"  全名: {admin_full_name}")
        print(f"  角色: 管理员")
        print(f"  用户ID: {admin_user.id}")
        
        print("\n🎉 现在可以使用以下信息登录管理后台：")
        print(f"  用户名: {admin_username}")
        print(f"  密码: {admin_password}")
        print(f"  后台地址: http://localhost:5173")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 创建管理员用户失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    create_admin_user()
