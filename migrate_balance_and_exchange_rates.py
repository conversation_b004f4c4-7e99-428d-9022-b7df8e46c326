#!/usr/bin/env python3
"""
数据库迁移脚本：添加用户余额字段和汇率表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config import settings
from app.database import get_db
from app import models

def migrate_database():
    """执行数据库迁移"""
    engine = create_engine(settings.database_url)
    
    try:
        with engine.connect() as conn:
            print("开始数据库迁移...")
            
            # 1. 添加用户余额字段
            print("1. 添加用户余额字段...")
            try:
                conn.execute(text("""
                    ALTER TABLE users 
                    ADD COLUMN balance_usd DECIMAL(15,2) DEFAULT 0.00,
                    ADD COLUMN balance_ngn DECIMAL(15,2) DEFAULT 0.00
                """))
                print("   ✓ 用户余额字段添加成功")
            except Exception as e:
                if "Duplicate column name" in str(e) or "already exists" in str(e):
                    print("   - 用户余额字段已存在，跳过")
                else:
                    print(f"   ✗ 添加用户余额字段失败: {e}")
                    raise
            
            # 2. 创建汇率表
            print("2. 创建汇率表...")
            try:
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS exchange_rates (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        from_currency ENUM('USD', 'NGN') NOT NULL,
                        to_currency ENUM('USD', 'NGN') NOT NULL,
                        rate DECIMAL(10,4) NOT NULL,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_exchange_rate_unique (from_currency, to_currency, is_active)
                    )
                """))
                print("   ✓ 汇率表创建成功")
            except Exception as e:
                print(f"   ✗ 创建汇率表失败: {e}")
                raise
            
            # 3. 插入默认汇率数据
            print("3. 插入默认汇率数据...")
            try:
                # 检查是否已有汇率数据
                result = conn.execute(text("SELECT COUNT(*) as count FROM exchange_rates")).fetchone()
                if result.count == 0:
                    # 插入默认汇率：1 USD = 1500 NGN
                    conn.execute(text("""
                        INSERT INTO exchange_rates (from_currency, to_currency, rate) VALUES
                        ('USD', 'NGN', 1500.0000),
                        ('NGN', 'USD', 0.0007)
                    """))
                    print("   ✓ 默认汇率数据插入成功 (1 USD = 1500 NGN)")
                else:
                    print("   - 汇率数据已存在，跳过")
            except Exception as e:
                print(f"   ✗ 插入默认汇率数据失败: {e}")
                raise
            
            # 4. 更新现有用户的余额为0
            print("4. 初始化现有用户余额...")
            try:
                conn.execute(text("""
                    UPDATE users 
                    SET balance_usd = 0.00, balance_ngn = 0.00 
                    WHERE balance_usd IS NULL OR balance_ngn IS NULL
                """))
                print("   ✓ 现有用户余额初始化完成")
            except Exception as e:
                print(f"   ✗ 初始化用户余额失败: {e}")
                raise
            
            conn.commit()
            print("\n✅ 数据库迁移完成！")
            
            # 显示迁移结果
            print("\n📊 迁移结果:")
            
            # 检查用户表结构
            result = conn.execute(text("DESCRIBE users")).fetchall()
            balance_fields = [row for row in result if 'balance' in row[0]]
            print(f"   用户余额字段: {len(balance_fields)} 个")
            for field in balance_fields:
                print(f"     - {field[0]}: {field[1]}")
            
            # 检查汇率表
            result = conn.execute(text("SELECT COUNT(*) as count FROM exchange_rates")).fetchone()
            print(f"   汇率记录数: {result.count} 条")
            
            # 显示当前汇率
            rates = conn.execute(text("SELECT from_currency, to_currency, rate FROM exchange_rates WHERE is_active = 1")).fetchall()
            print("   当前汇率:")
            for rate in rates:
                print(f"     - {rate[0]} → {rate[1]}: {rate[2]}")
                
    except Exception as e:
        print(f"\n❌ 数据库迁移失败: {e}")
        raise

if __name__ == "__main__":
    migrate_database()
