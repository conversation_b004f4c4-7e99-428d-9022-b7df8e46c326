#!/usr/bin/env python3
"""
数据库迁移脚本 - 添加新字段和表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.database import engine, get_db
from app.models import Base

def migrate_database():
    """执行数据库迁移"""
    print("开始数据库迁移...")
    
    try:
        with engine.connect() as connection:
            # 1. 为用户表添加默认货币字段
            print("1. 为用户表添加默认货币字段...")
            try:
                connection.execute(text("""
                    ALTER TABLE users 
                    ADD COLUMN default_currency ENUM('USD', 'NGN') DEFAULT 'USD'
                """))
                connection.commit()
                print("   ✓ 用户表default_currency字段添加成功")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    print("   - 用户表default_currency字段已存在，跳过")
                else:
                    print(f"   ✗ 添加用户表default_currency字段失败: {e}")
            
            # 2. 为商品表添加货币字段
            print("2. 为商品表添加货币字段...")
            try:
                connection.execute(text("""
                    ALTER TABLE products 
                    ADD COLUMN currency ENUM('USD', 'NGN') DEFAULT 'USD'
                """))
                connection.commit()
                print("   ✓ 商品表currency字段添加成功")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    print("   - 商品表currency字段已存在，跳过")
                else:
                    print(f"   ✗ 添加商品表currency字段失败: {e}")
            
            # 3. 为交易表添加新字段
            print("3. 为交易表添加新字段...")
            try:
                connection.execute(text("""
                    ALTER TABLE transactions 
                    ADD COLUMN currency ENUM('USD', 'NGN') DEFAULT 'USD',
                    ADD COLUMN transaction_type ENUM('sell', 'buy') DEFAULT 'sell',
                    ADD COLUMN gift_card_info TEXT
                """))
                connection.commit()
                print("   ✓ 交易表新字段添加成功")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    print("   - 交易表新字段已存在，跳过")
                else:
                    print(f"   ✗ 添加交易表新字段失败: {e}")
            
            # 4. 创建交易图片表
            print("4. 创建交易图片表...")
            try:
                connection.execute(text("""
                    CREATE TABLE IF NOT EXISTS transaction_images (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        transaction_id INT NOT NULL,
                        image_url VARCHAR(255) NOT NULL,
                        image_type VARCHAR(50),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
                        INDEX idx_transaction_id (transaction_id)
                    )
                """))
                connection.commit()
                print("   ✓ 交易图片表创建成功")
            except Exception as e:
                print(f"   ✗ 创建交易图片表失败: {e}")
            
            # 5. 创建提现记录表
            print("5. 创建提现记录表...")
            try:
                connection.execute(text("""
                    CREATE TABLE IF NOT EXISTS withdrawals (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        amount DECIMAL(10,2) NOT NULL,
                        currency ENUM('USD', 'NGN') DEFAULT 'USD',
                        bank_account VARCHAR(255) NOT NULL,
                        bank_name VARCHAR(100),
                        account_name VARCHAR(100),
                        notes TEXT,
                        status ENUM('pending', 'processing', 'completed', 'rejected') DEFAULT 'pending',
                        processed_at TIMESTAMP NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                        INDEX idx_user_id (user_id),
                        INDEX idx_status (status)
                    )
                """))
                connection.commit()
                print("   ✓ 提现记录表创建成功")
            except Exception as e:
                print(f"   ✗ 创建提现记录表失败: {e}")
            
            # 6. 修改交易表，允许product_id为空（卖出时不需要关联商品）
            print("6. 修改交易表product_id字段...")
            try:
                connection.execute(text("""
                    ALTER TABLE transactions 
                    MODIFY COLUMN product_id INT NULL,
                    MODIFY COLUMN buyer_id INT NULL
                """))
                connection.commit()
                print("   ✓ 交易表字段修改成功")
            except Exception as e:
                print(f"   ✗ 修改交易表字段失败: {e}")
        
        print("\n✅ 数据库迁移完成！")
        
    except Exception as e:
        print(f"\n❌ 数据库迁移失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = migrate_database()
    if not success:
        sys.exit(1)
