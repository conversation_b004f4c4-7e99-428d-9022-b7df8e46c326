<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小黑聊天客户端示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 4px;
        }
        .message.sent {
            background-color: #e3f2fd;
            text-align: right;
        }
        .message.received {
            background-color: #f3e5f5;
        }
        .message.system {
            background-color: #fff3e0;
            font-style: italic;
        }
        input, button {
            padding: 8px;
            margin: 5px;
        }
        input[type="text"] {
            width: 200px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #c8e6c9;
            color: #2e7d32;
        }
        .status.disconnected {
            background-color: #ffcdd2;
            color: #c62828;
        }
        .online-users {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🗨️ 小黑聊天客户端示例</h1>
    
    <div class="container">
        <h3>连接设置</h3>
        <div>
            <label>用户ID: </label>
            <input type="text" id="userId" value="1" placeholder="输入用户ID">
        </div>
        <div>
            <label>访问令牌: </label>
            <input type="text" id="accessToken" placeholder="输入JWT访问令牌" style="width: 400px;">
        </div>
        <div>
            <button onclick="connect()">连接</button>
            <button onclick="disconnect()">断开连接</button>
        </div>
        <div id="connectionStatus" class="status disconnected">未连接</div>
    </div>

    <div class="container">
        <h3>聊天</h3>
        <div id="messages" class="messages"></div>
        <div>
            <label>接收者ID: </label>
            <input type="text" id="receiverId" placeholder="输入接收者用户ID">
            <label>消息: </label>
            <input type="text" id="messageInput" placeholder="输入消息内容" style="width: 300px;">
            <button onclick="sendMessage()">发送消息</button>
        </div>
        <div>
            <button onclick="getOnlineUsers()">获取在线用户</button>
            <button onclick="sendPing()">发送心跳</button>
        </div>
    </div>

    <div class="container">
        <h3>在线用户</h3>
        <div id="onlineUsers" class="online-users">暂无在线用户</div>
    </div>

    <script>
        let ws = null;
        let currentUserId = null;

        function connect() {
            const userId = document.getElementById('userId').value;
            const accessToken = document.getElementById('accessToken').value;
            
            if (!userId || !accessToken) {
                alert('请输入用户ID和访问令牌');
                return;
            }

            currentUserId = userId;
            const wsUrl = `ws://localhost:8000/api/v1/chat/ws/${userId}?token=${accessToken}`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                updateConnectionStatus(true);
                addMessage('系统', '连接已建立', 'system');
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleMessage(data);
            };
            
            ws.onclose = function(event) {
                updateConnectionStatus(false);
                addMessage('系统', '连接已断开', 'system');
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
                addMessage('系统', '连接错误', 'system');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('请先连接到服务器');
                return;
            }

            const receiverId = document.getElementById('receiverId').value;
            const messageContent = document.getElementById('messageInput').value;
            
            if (!receiverId || !messageContent) {
                alert('请输入接收者ID和消息内容');
                return;
            }

            const message = {
                type: 'chat_message',
                receiver_id: parseInt(receiverId),
                content: messageContent
            };

            ws.send(JSON.stringify(message));
            document.getElementById('messageInput').value = '';
            
            // 显示发送的消息
            addMessage('我', messageContent, 'sent');
        }

        function getOnlineUsers() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('请先连接到服务器');
                return;
            }

            const message = {
                type: 'get_online_users'
            };

            ws.send(JSON.stringify(message));
        }

        function sendPing() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('请先连接到服务器');
                return;
            }

            const message = {
                type: 'ping',
                timestamp: Date.now()
            };

            ws.send(JSON.stringify(message));
        }

        function handleMessage(data) {
            switch(data.type) {
                case 'private_message':
                    addMessage(data.sender_name, data.message, 'received');
                    break;
                case 'message_sent':
                    addMessage('系统', '消息已发送', 'system');
                    break;
                case 'user_status':
                    addMessage('系统', `${data.username} ${data.status === 'online' ? '上线了' : '下线了'}`, 'system');
                    break;
                case 'online_users':
                    updateOnlineUsers(data.users);
                    break;
                case 'pong':
                    addMessage('系统', '心跳响应', 'system');
                    break;
                case 'error':
                    addMessage('错误', data.message, 'system');
                    break;
                default:
                    console.log('未知消息类型:', data);
            }
        }

        function addMessage(sender, content, type) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<strong>${sender}</strong> [${timestamp}]: ${content}`;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            const statusDiv = document.getElementById('connectionStatus');
            if (connected) {
                statusDiv.textContent = '已连接';
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = '未连接';
                statusDiv.className = 'status disconnected';
            }
        }

        function updateOnlineUsers(users) {
            const onlineUsersDiv = document.getElementById('onlineUsers');
            if (users.length === 0) {
                onlineUsersDiv.textContent = '暂无其他在线用户';
            } else {
                const userList = users.map(user => 
                    `${user.username} (ID: ${user.user_id})`
                ).join(', ');
                onlineUsersDiv.textContent = `在线用户: ${userList}`;
            }
        }

        // 页面加载时的提示
        window.onload = function() {
            addMessage('系统', '欢迎使用小黑聊天客户端！请先登录获取访问令牌，然后连接到服务器。', 'system');
        };
    </script>
</body>
</html>
