#!/usr/bin/env python3
"""
API 测试脚本
"""

import requests
import json
import websocket
import threading
import time

BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000"

def test_user_registration_and_login():
    """测试用户注册和登录"""
    print("🧪 测试用户注册和登录...")
    
    # 注册用户
    register_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "测试用户"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/users/register", json=register_data)
    if response.status_code == 200:
        print("✅ 用户注册成功")
        user_data = response.json()
        print(f"   用户ID: {user_data['id']}")
    else:
        print(f"❌ 用户注册失败: {response.text}")
        return None
    
    # 登录
    login_data = {
        "username": "testuser",
        "password": "testpassword123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/users/login", json=login_data)
    if response.status_code == 200:
        print("✅ 用户登录成功")
        token_data = response.json()
        access_token = token_data["access_token"]
        print(f"   访问令牌: {access_token[:20]}...")
        return access_token, user_data['id']
    else:
        print(f"❌ 用户登录失败: {response.text}")
        return None


def test_product_operations(access_token):
    """测试商品操作"""
    print("\n🧪 测试商品操作...")
    
    headers = {"Authorization": f"Bearer {access_token}"}
    
    # 创建商品
    product_data = {
        "title": "测试商品",
        "description": "这是一个测试商品",
        "price": 99.99,
        "category": "测试分类"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/products/", json=product_data, headers=headers)
    if response.status_code == 200:
        print("✅ 商品创建成功")
        product = response.json()
        print(f"   商品ID: {product['id']}")
        return product['id']
    else:
        print(f"❌ 商品创建失败: {response.text}")
        return None


def test_websocket_chat(access_token, user_id):
    """测试 WebSocket 聊天"""
    print("\n🧪 测试 WebSocket 聊天...")
    
    def on_message(ws, message):
        data = json.loads(message)
        print(f"📨 收到消息: {data}")
    
    def on_error(ws, error):
        print(f"❌ WebSocket 错误: {error}")
    
    def on_close(ws, close_status_code, close_msg):
        print("🔌 WebSocket 连接已关闭")
    
    def on_open(ws):
        print("✅ WebSocket 连接已建立")
        
        # 发送测试消息
        time.sleep(1)
        test_message = {
            "type": "get_online_users"
        }
        ws.send(json.dumps(test_message))
        
        # 发送心跳
        time.sleep(1)
        ping_message = {
            "type": "ping",
            "timestamp": int(time.time() * 1000)
        }
        ws.send(json.dumps(ping_message))
        
        # 5秒后关闭连接
        time.sleep(5)
        ws.close()
    
    ws_url = f"{WS_URL}/api/v1/chat/ws/{user_id}?token={access_token}"
    ws = websocket.WebSocketApp(
        ws_url,
        on_open=on_open,
        on_message=on_message,
        on_error=on_error,
        on_close=on_close
    )
    
    # 在新线程中运行 WebSocket
    ws_thread = threading.Thread(target=ws.run_forever)
    ws_thread.daemon = True
    ws_thread.start()
    
    # 等待测试完成
    ws_thread.join(timeout=10)


def test_api_endpoints():
    """测试基本 API 端点"""
    print("🧪 测试基本 API 端点...")
    
    # 测试根端点
    response = requests.get(f"{BASE_URL}/")
    if response.status_code == 200:
        print("✅ 根端点正常")
    else:
        print("❌ 根端点异常")
    
    # 测试健康检查
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        print("✅ 健康检查正常")
    else:
        print("❌ 健康检查异常")


def main():
    """主测试函数"""
    print("🚀 开始 API 测试...")
    print("=" * 50)
    
    # 测试基本端点
    test_api_endpoints()
    
    # 测试用户注册和登录
    auth_result = test_user_registration_and_login()
    if not auth_result:
        print("❌ 认证测试失败，跳过后续测试")
        return
    
    access_token, user_id = auth_result
    
    # 测试商品操作
    product_id = test_product_operations(access_token)
    
    # 测试 WebSocket 聊天
    try:
        test_websocket_chat(access_token, user_id)
    except Exception as e:
        print(f"❌ WebSocket 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")


if __name__ == "__main__":
    main()
