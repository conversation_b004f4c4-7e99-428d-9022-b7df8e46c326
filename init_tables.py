#!/usr/bin/env python3
"""
数据库表初始化脚本
在远程数据库中创建所需的表结构
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import engine
from app import models

def init_database_tables():
    """初始化数据库表"""
    try:
        print("🚀 开始创建数据库表...")
        
        # 创建所有表
        models.Base.metadata.create_all(bind=engine)
        
        print("✅ 数据库表创建成功！")
        print("\n📋 已创建的表：")
        print("  - users (用户表)")
        print("  - products (商品表)")
        print("  - transactions (交易表)")
        print("  - messages (消息表)")
        
        print("\n🎉 数据库初始化完成！")
        print("现在可以启动服务了：python start.py")
        
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    init_database_tables()
