#!/usr/bin/env python3
"""
数据库初始化脚本
创建数据库和用户
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import sys

def create_database():
    """创建数据库和用户"""
    try:
        # 连接到 PostgreSQL 默认数据库
        conn = psycopg2.connect(
            host="localhost",
            database="postgres",
            user="postgres",
            password="postgres"  # 请根据你的 PostgreSQL 安装修改密码
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 创建用户
        try:
            cursor.execute("CREATE USER xiaohei WITH PASSWORD 'ftGN5JB5TB6yC2jd';")
            print("✅ 用户 xiaohei 创建成功")
        except psycopg2.errors.DuplicateObject:
            print("ℹ️  用户 xiaohei 已存在")
        
        # 创建数据库
        try:
            cursor.execute("CREATE DATABASE xiaohei_db OWNER xiaohei;")
            print("✅ 数据库 xiaohei_db 创建成功")
        except psycopg2.errors.DuplicateDatabase:
            print("ℹ️  数据库 xiaohei_db 已存在")
        
        # 授予权限
        cursor.execute("GRANT ALL PRIVILEGES ON DATABASE xiaohei_db TO xiaohei;")
        print("✅ 权限授予成功")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 数据库初始化完成！")
        print("数据库连接信息：")
        print("  主机: localhost")
        print("  端口: 5432")
        print("  数据库: xiaohei_db")
        print("  用户名: xiaohei")
        print("  密码: ftGN5JB5TB6yC2jd")
        
    except psycopg2.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("🚀 开始初始化数据库...")
    create_database()
