#!/usr/bin/env python3
"""
数据库连接测试脚本
测试数据库连接并确认配置
"""

import psycopg2
import sys

def test_database_connection():
    """测试数据库连接"""
    try:
        # 直接连接到目标数据库
        conn = psycopg2.connect(
            host="************",
            database="xiaohei",
            user="xiaohei",
            password="ftGN5JB5TB6yC2jd",
            port=5432
        )

        cursor = conn.cursor()

        # 测试连接
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print("✅ 数据库连接成功！")
        print(f"   PostgreSQL 版本: {version[0]}")

        # 检查当前数据库
        cursor.execute("SELECT current_database();")
        current_db = cursor.fetchone()
        print(f"   当前数据库: {current_db[0]}")

        # 检查当前用户
        cursor.execute("SELECT current_user;")
        current_user = cursor.fetchone()
        print(f"   当前用户: {current_user[0]}")

        cursor.close()
        conn.close()
        
        print("\n🎉 数据库初始化完成！")
        print("数据库连接信息：")
        print("  主机: ************")
        print("  端口: 5432")
        print("  数据库: xiaohei")
        print("  用户名: xiaohei")
        print("  密码: ftGN5JB5TB6yC2jd")
        
    except psycopg2.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("🚀 开始测试数据库连接...")
    test_database_connection()
