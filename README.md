# 小黑后端服务

基于 FastAPI 的后端服务，包含用户管理、商品交易、消息系统和 WebSocket 实时聊天功能。

## 功能特性

### 🔐 用户模块
- 用户注册和登录
- JWT 身份认证
- 个人信息管理
- 用户列表查看

### 💬 消息模块
- 系统消息
- 用户私信
- 消息列表和详情
- 消息已读状态管理
- 会话列表

### 🛒 商品模块
- 商品发布和管理
- 商品列表和搜索
- 商品分类
- 商品详情查看

### 💰 交易模块
- 交易创建和管理
- 交易状态跟踪
- 买家和卖家视图
- 交易完成流程

### 💬 实时聊天 (WebSocket)
- 实时消息发送和接收
- 在线用户状态
- 用户上线/下线通知
- 通过用户 ID 识别客户端
- 心跳检测

## 技术栈

- **FastAPI**: 现代、快速的 Web 框架
- **SQLAlchemy**: ORM 数据库操作
- **PostgreSQL**: 数据库
- **JWT**: 身份认证
- **WebSocket**: 实时通信
- **Pydantic**: 数据验证
- **Uvicorn**: ASGI 服务器

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境变量
`.env` 文件已配置好远程数据库连接，无需修改。

### 3. 测试数据库连接
测试与远程数据库的连接：
```bash
python create_database.py
```

### 4. 初始化数据库表
创建所需的数据库表：
```bash
python init_tables.py
```

### 5. 启动服务
```bash
python start.py
```

服务将在 `http://localhost:8000` 启动。

## API 文档

启动服务后，访问以下地址查看 API 文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## WebSocket 聊天使用

### 连接
```javascript
const token = "your_jwt_token";
const userId = 123;
const ws = new WebSocket(`ws://localhost:8000/api/v1/chat/ws/${userId}?token=${token}`);
```

### 发送消息
```javascript
// 发送聊天消息
ws.send(JSON.stringify({
    type: "chat_message",
    receiver_id: 456,
    content: "Hello!"
}));

// 获取在线用户
ws.send(JSON.stringify({
    type: "get_online_users"
}));

// 心跳检测
ws.send(JSON.stringify({
    type: "ping",
    timestamp: Date.now()
}));
```

### 接收消息
```javascript
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case "private_message":
            console.log("收到私信:", data);
            break;
        case "user_status":
            console.log("用户状态变化:", data);
            break;
        case "online_users":
            console.log("在线用户列表:", data.users);
            break;
        case "pong":
            console.log("心跳响应:", data);
            break;
    }
};
```

## 数据库模型

### 用户 (User)
- 基本信息：用户名、邮箱、全名、电话
- 认证信息：密码哈希、角色、状态
- 关联：商品、消息、交易

### 商品 (Product)
- 商品信息：标题、描述、价格、图片、分类
- 状态：是否可用
- 关联：所有者、交易记录

### 交易 (Transaction)
- 交易信息：商品、买家、卖家、金额
- 状态：待处理、已确认、已完成、已取消
- 备注信息

### 消息 (Message)
- 消息内容：标题、内容、类型
- 状态：是否已读
- 关联：发送者、接收者

## 开发说明

### 项目结构
```
├── app/
│   ├── __init__.py
│   ├── main.py              # 应用入口
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库连接
│   ├── models.py            # 数据库模型
│   ├── schemas.py           # Pydantic 模式
│   ├── auth.py              # 认证相关
│   ├── websocket_manager.py # WebSocket 管理
│   └── routers/             # 路由模块
│       ├── users.py         # 用户路由
│       ├── messages.py      # 消息路由
│       ├── products.py      # 商品路由
│       ├── transactions.py  # 交易路由
│       └── chat.py          # 聊天路由
├── requirements.txt         # 依赖列表
├── .env                     # 环境变量
├── create_database.py       # 数据库初始化
├── start.py                 # 启动脚本
└── README.md               # 项目说明
```

### 环境变量说明
- `DATABASE_URL`: PostgreSQL 数据库连接字符串 (*******************************************************/xiaohei)
- `SECRET_KEY`: JWT 签名密钥
- `ACCESS_TOKEN_EXPIRE_MINUTES`: 访问令牌过期时间（分钟）

## 许可证

MIT License
