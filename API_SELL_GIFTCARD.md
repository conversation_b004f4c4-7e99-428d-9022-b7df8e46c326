# 礼品卡卖出接口文档

## 概述
礼品卡回收系统提供两种卖出接口：
1. **推荐使用**: `/api/v1/transactions/sell-with-image` - 一次性创建交易并上传图片
2. **备用方案**: `/api/v1/transactions/sell` + `/api/v1/transactions/upload-image/{transaction_id}` - 分步操作

## 1. 一次性卖出接口（推荐）

### 接口地址
```
POST /api/v1/transactions/sell-with-image
```

### 请求方式
`multipart/form-data`

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| product_id | int | 是 | 商品ID（如：1=DavidCard, 2=SteamCard, 3=AmazonCard） |
| amount | float | 是 | 卖出金额 |
| currency | string | 否 | 货币类型，默认USD |
| notes | string | 否 | 备注信息 |
| gift_card_info | string | 否 | 礼品卡信息（如卡号等） |
| image | file | 是 | 礼品卡图片文件（支持jpg, png, gif） |

### 请求示例
```bash
curl -X POST "http://localhost:8000/api/v1/transactions/sell-with-image" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "product_id=2" \
  -F "amount=50.00" \
  -F "currency=USD" \
  -F "notes=测试卖出Steam礼品卡" \
  -F "gift_card_info=Steam卡号: ABCD-EFGH-IJKL" \
  -F "image=@giftcard.png"
```

### 响应示例
```json
{
  "code": 200,
  "message": "卖出交易创建成功，图片上传完成",
  "data": {
    "id": 6,
    "product_id": 2,
    "amount": 50.0,
    "currency": "USD",
    "transaction_type": "sell",
    "status": "pending",
    "notes": "测试卖出Steam礼品卡",
    "gift_card_info": "Steam卡号: ABCD-EFGH-IJKL",
    "seller_id": 10,
    "image_url": "/uploads/transaction_images/9c78e1d7-90d2-4552-99b2-1f8b656ba3f1.png",
    "created_at": "2025-06-30 16:11:30"
  },
  "success": true
}
```

## 2. 分步操作接口（备用）

### 第一步：创建交易
```
POST /api/v1/transactions/sell
Content-Type: application/json

{
  "product_id": 2,
  "amount": 50.00,
  "currency": "USD",
  "notes": "测试卖出Steam礼品卡",
  "gift_card_info": "Steam卡号: ABCD-EFGH-IJKL"
}
```

### 第二步：上传图片
```
POST /api/v1/transactions/upload-image/{transaction_id}
Content-Type: multipart/form-data

file: [图片文件]
image_type: "gift_card"
```

## 3. 查看交易详情

### 接口地址
```
GET /api/v1/transactions/{transaction_id}
```

### 响应示例
```json
{
  "code": 200,
  "message": "获取交易详情成功",
  "data": {
    "id": 6,
    "product_id": 2,
    "buyer_id": null,
    "seller_id": 10,
    "amount": 50.0,
    "currency": "USD",
    "transaction_type": "sell",
    "status": "pending",
    "notes": "测试卖出Steam礼品卡",
    "gift_card_info": "Steam卡号: ABCD-EFGH-IJKL",
    "images": [
      {
        "id": 2,
        "url": "/uploads/transaction_images/9c78e1d7-90d2-4552-99b2-1f8b656ba3f1.png",
        "type": "gift_card"
      }
    ],
    "created_at": "2025-06-30 16:11:30"
  },
  "success": true
}
```

## 4. 可用商品列表

### 接口地址
```
GET /api/v1/products/
```

### 响应示例
```json
{
  "code": 200,
  "message": "获取礼品卡回收商品列表成功",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "DavidCard",
        "description": "专业礼品卡回收服务，快速安全",
        "min_amount": 10.0,
        "max_amount": 500.0,
        "currency": "USD",
        "category": "Razer",
        "average_time_minutes": 5,
        "transaction_count": 127671,
        "like_count": 11889,
        "success_rate": 100.0
      }
    ]
  }
}
```

## 注意事项

1. **金额验证**: 卖出金额必须在商品的 `min_amount` 和 `max_amount` 范围内
2. **图片要求**: 支持 jpg, png, gif 格式，建议大小不超过 5MB
3. **权限要求**: 需要用户登录，获取有效的 JWT token
4. **统计更新**: 每次成功创建交易后，对应商品的交易量会自动增加
5. **状态说明**: 新创建的交易状态为 "pending"，等待管理员处理

## 错误码说明

- `400`: 参数错误（如金额超出范围、文件格式不支持等）
- `401`: 未授权（token无效或过期）
- `404`: 商品不存在
- `500`: 服务器内部错误
