from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Numeric, Enum, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum


class UserRole(enum.Enum):
    USER = "user"
    ADMIN = "admin"


class TransactionStatus(enum.Enum):
    PENDING = "pending"      # 待处理
    CONFIRMED = "confirmed"  # 已确认
    COMPLETED = "completed"  # 已完成
    CANCELLED = "cancelled"  # 已取消


class TransactionType(enum.Enum):
    SELL = "sell"        # 卖出
    BUY = "buy"          # 购买


class CurrencyType(enum.Enum):
    USD = "USD"          # 美元
    NGN = "NGN"          # 尼日利亚奈拉


class WithdrawalStatus(enum.Enum):
    PENDING = "pending"      # 待处理
    PROCESSING = "processing" # 处理中
    COMPLETED = "completed"   # 已完成
    REJECTED = "rejected"     # 已拒绝


class MessageType(enum.Enum):
    SYSTEM = "system"    # 系统消息
    USER = "user"        # 用户消息
    CHAT = "chat"        # 聊天消息


# 用户模型
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    phone = Column(String(20))
    avatar_url = Column(String(255))
    role = Column(Enum(UserRole), default=UserRole.USER)
    default_currency = Column(Enum(CurrencyType), default=CurrencyType.USD)  # 默认货币
    balance_usd = Column(Numeric(15, 2), default=0.00)  # USD余额
    balance_ngn = Column(Numeric(15, 2), default=0.00)  # NGN余额
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    sent_messages = relationship("Message", foreign_keys="Message.sender_id", back_populates="sender")
    received_messages = relationship("Message", foreign_keys="Message.receiver_id", back_populates="receiver")
    buyer_transactions = relationship("Transaction", foreign_keys="Transaction.buyer_id", back_populates="buyer")
    seller_transactions = relationship("Transaction", foreign_keys="Transaction.seller_id", back_populates="seller")
    withdrawals = relationship("Withdrawal", back_populates="user")


# 商品模型 - 礼品卡回收商品
class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False)  # 商品名称，如 "DavidCard"
    avatar_url = Column(String(255))  # 头像URL
    description = Column(Text)  # 商品描述
    min_amount = Column(Numeric(10, 2), nullable=False)  # 最小接收金额
    max_amount = Column(Numeric(10, 2), nullable=False)  # 最大接收金额
    currency = Column(Enum(CurrencyType), default=CurrencyType.USD)  # 货币类型
    average_time_minutes = Column(Integer, default=5)  # 平均处理时间（分钟）
    transaction_count = Column(Integer, default=0)  # 交易量统计
    like_count = Column(Integer, default=0)  # 点赞数量
    success_rate = Column(Numeric(5, 2), default=100.00)  # 成功率百分比
    is_available = Column(Boolean, default=True)  # 是否可用
    category = Column(String(50))  # 分类，如 "Razer", "Steam" 等
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    transactions = relationship("Transaction", back_populates="product")


# 交易模型
class Transaction(Base):
    __tablename__ = "transactions"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=True)  # 关联的商品ID
    buyer_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # 买家ID（系统处理时可为空）
    seller_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # 卖家ID（用户）
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(Enum(CurrencyType), default=CurrencyType.USD)  # 交易货币类型
    transaction_type = Column(Enum(TransactionType), default=TransactionType.SELL)  # 交易类型
    status = Column(Enum(TransactionStatus), default=TransactionStatus.PENDING)
    notes = Column(Text)
    gift_card_info = Column(Text)  # 礼品卡信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    product = relationship("Product", back_populates="transactions")
    buyer = relationship("User", foreign_keys=[buyer_id], back_populates="buyer_transactions")
    seller = relationship("User", foreign_keys=[seller_id], back_populates="seller_transactions")
    images = relationship("TransactionImage", back_populates="transaction")


# 交易图片模型
class TransactionImage(Base):
    __tablename__ = "transaction_images"

    id = Column(Integer, primary_key=True, index=True)
    transaction_id = Column(Integer, ForeignKey("transactions.id"), nullable=False)
    image_url = Column(String(255), nullable=False)
    image_type = Column(String(50))  # 图片类型：gift_card, receipt等
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    transaction = relationship("Transaction", back_populates="images")


# 提现记录模型
class Withdrawal(Base):
    __tablename__ = "withdrawals"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(Enum(CurrencyType), default=CurrencyType.USD)
    bank_account = Column(String(255), nullable=False)  # 银行账号
    bank_name = Column(String(100))  # 银行名称
    account_name = Column(String(100))  # 账户名称
    notes = Column(Text)  # 备注
    status = Column(Enum(WithdrawalStatus), default=WithdrawalStatus.PENDING)
    processed_at = Column(DateTime(timezone=True))  # 处理时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    user = relationship("User", back_populates="withdrawals")


# 消息模型
class Message(Base):
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    sender_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    receiver_id = Column(Integer, ForeignKey("users.id"))
    message_type = Column(Enum(MessageType), default=MessageType.USER)
    title = Column(String(200))
    content = Column(Text, nullable=False)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    sender = relationship("User", foreign_keys=[sender_id], back_populates="sent_messages")
    receiver = relationship("User", foreign_keys=[receiver_id], back_populates="received_messages")


# 汇率设置模型
class ExchangeRate(Base):
    __tablename__ = "exchange_rates"

    id = Column(Integer, primary_key=True, index=True)
    from_currency = Column(Enum(CurrencyType), nullable=False)  # 源货币
    to_currency = Column(Enum(CurrencyType), nullable=False)    # 目标货币
    rate = Column(Numeric(10, 4), nullable=False)               # 汇率
    is_active = Column(Boolean, default=True)                   # 是否启用
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
