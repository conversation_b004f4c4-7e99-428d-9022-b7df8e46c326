from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from app.models import UserRole, TransactionStatus, MessageType


# 用户相关模式
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    phone: Optional[str] = None


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None


class User(UserBase):
    id: int
    avatar_url: Optional[str] = None
    role: UserRole
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


# 认证相关模式
class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    username: Optional[str] = None


# 商品相关模式
class ProductBase(BaseModel):
    title: str
    description: Optional[str] = None
    price: Decimal
    category: Optional[str] = None


class ProductCreate(ProductBase):
    image_url: Optional[str] = None


class ProductUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[Decimal] = None
    image_url: Optional[str] = None
    category: Optional[str] = None
    is_available: Optional[bool] = None


class Product(ProductBase):
    id: int
    image_url: Optional[str] = None
    is_available: bool
    owner_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


# 交易相关模式
class TransactionBase(BaseModel):
    product_id: int
    amount: Decimal
    notes: Optional[str] = None


class TransactionCreate(TransactionBase):
    pass


class TransactionUpdate(BaseModel):
    status: Optional[TransactionStatus] = None
    notes: Optional[str] = None


class Transaction(TransactionBase):
    id: int
    buyer_id: int
    seller_id: int
    status: TransactionStatus
    created_at: datetime
    
    class Config:
        from_attributes = True


# 消息相关模式
class MessageBase(BaseModel):
    title: Optional[str] = None
    content: str


class MessageCreate(MessageBase):
    receiver_id: Optional[int] = None
    message_type: MessageType = MessageType.USER


class Message(MessageBase):
    id: int
    sender_id: int
    receiver_id: Optional[int] = None
    message_type: MessageType
    is_read: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


# 响应模式
class ResponseModel(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None
