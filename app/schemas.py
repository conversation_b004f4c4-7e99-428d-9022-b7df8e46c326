from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from app.models import UserRole, TransactionStatus, MessageType, TransactionType, CurrencyType, WithdrawalStatus


# 用户相关模式
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    phone: Optional[str] = None


class UserCreate(UserBase):
    password: str


class UserLogin(BaseModel):
    username: str
    password: str


class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    default_currency: Optional[CurrencyType] = None


class User(UserBase):
    id: int
    avatar_url: Optional[str] = None
    role: UserRole
    default_currency: CurrencyType
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


# 认证相关模式
class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    username: Optional[str] = None


# 商品相关模式
class ProductBase(BaseModel):
    title: str
    description: Optional[str] = None
    price: Decimal
    currency: CurrencyType = CurrencyType.USD
    category: Optional[str] = None


class ProductCreate(ProductBase):
    image_url: Optional[str] = None


class ProductUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[Decimal] = None
    currency: Optional[CurrencyType] = None
    image_url: Optional[str] = None
    category: Optional[str] = None
    is_available: Optional[bool] = None


class Product(ProductBase):
    id: int
    image_url: Optional[str] = None
    is_available: bool
    owner_id: int
    created_at: datetime

    class Config:
        from_attributes = True


# 交易相关模式
class TransactionBase(BaseModel):
    amount: Decimal
    currency: CurrencyType = CurrencyType.USD
    notes: Optional[str] = None
    gift_card_info: Optional[str] = None


class TransactionCreate(TransactionBase):
    """卖出礼品卡"""
    pass


class TransactionUpdate(BaseModel):
    status: Optional[TransactionStatus] = None
    notes: Optional[str] = None


class Transaction(TransactionBase):
    id: int
    product_id: Optional[int] = None
    buyer_id: Optional[int] = None
    seller_id: int
    transaction_type: TransactionType
    status: TransactionStatus
    created_at: datetime

    class Config:
        from_attributes = True


# 交易图片相关模式
class TransactionImageCreate(BaseModel):
    image_type: Optional[str] = "gift_card"


class TransactionImage(BaseModel):
    id: int
    transaction_id: int
    image_url: str
    image_type: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


# 提现相关模式
class WithdrawalCreate(BaseModel):
    amount: Decimal
    currency: CurrencyType = CurrencyType.USD
    bank_account: str
    bank_name: Optional[str] = None
    account_name: Optional[str] = None
    notes: Optional[str] = None


class WithdrawalUpdate(BaseModel):
    status: Optional[WithdrawalStatus] = None
    notes: Optional[str] = None


class Withdrawal(BaseModel):
    id: int
    user_id: int
    amount: Decimal
    currency: CurrencyType
    bank_account: str
    bank_name: Optional[str] = None
    account_name: Optional[str] = None
    notes: Optional[str] = None
    status: WithdrawalStatus
    processed_at: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True


# 设置默认货币
class CurrencyUpdate(BaseModel):
    default_currency: CurrencyType


# 消息相关模式
class MessageBase(BaseModel):
    title: Optional[str] = None
    content: str


class MessageCreate(MessageBase):
    receiver_id: Optional[int] = None
    message_type: MessageType = MessageType.USER


class Message(MessageBase):
    id: int
    sender_id: int
    receiver_id: Optional[int] = None
    message_type: MessageType
    is_read: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


# WebSocket 聊天相关模式
class ChatMessage(BaseModel):
    type: str = "chat_message"
    receiver_id: int
    content: str


class WebSocketMessage(BaseModel):
    type: str
    data: dict


# 标准响应模式
class StandardResponse(BaseModel):
    """标准API响应格式"""
    code: int = 200  # 状态码
    message: str = "success"  # 响应消息
    data: Optional[dict] = None  # 响应数据
    success: bool = True  # 是否成功

class ListResponse(BaseModel):
    """列表响应格式"""
    code: int = 200
    message: str = "success"
    data: List[dict] = []
    total: Optional[int] = None  # 总数
    page: Optional[int] = None   # 当前页
    page_size: Optional[int] = None  # 每页大小
    success: bool = True

class ErrorResponse(BaseModel):
    """错误响应格式"""
    code: int
    message: str
    data: Optional[dict] = None
    success: bool = False

# 响应模式（保持向后兼容）
class ResponseModel(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None
