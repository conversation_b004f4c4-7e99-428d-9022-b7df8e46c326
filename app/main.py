from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.config import settings
from app.database import engine
from app import models
from app.routers import users, messages, products, transactions, chat, admin

# 创建数据库表
models.Base.metadata.create_all(bind=engine)

app = FastAPI(
    title=settings.app_name,
    description="小黑后端API服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(users.router, prefix="/api/v1/users", tags=["用户"])
app.include_router(messages.router, prefix="/api/v1/messages", tags=["消息"])
app.include_router(products.router, prefix="/api/v1/products", tags=["商品"])
app.include_router(transactions.router, prefix="/api/v1/transactions", tags=["交易"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["聊天"])
app.include_router(admin.router, prefix="/admin", tags=["管理员"])


@app.get("/")
async def root():
    return {"message": "欢迎使用小黑后端服务"}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}
