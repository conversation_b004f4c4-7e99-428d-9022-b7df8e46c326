from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app import models, schemas, auth
from app.routers.admin import get_admin_user
from app.response_utils import success_response, error_response
import uuid
import os
from pathlib import Path

router = APIRouter()

# 确保上传目录存在
UPLOAD_DIR = Path("uploads/categories")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)


@router.get("/", response_model=dict)
async def get_product_categories(
    page: int = 1,
    size: int = 20,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """获取商品种类列表"""
    try:
        query = db.query(models.ProductCategory)
        
        if is_active is not None:
            query = query.filter(models.ProductCategory.is_active == is_active)
        
        # 按排序顺序和创建时间排序
        query = query.order_by(models.ProductCategory.sort_order, models.ProductCategory.created_at)
        
        # 分页
        total = query.count()
        categories = query.offset((page - 1) * size).limit(size).all()
        
        categories_data = []
        for category in categories:
            categories_data.append({
                "id": category.id,
                "name": category.name,
                "display_name": category.display_name,
                "icon_url": category.icon_url,
                "is_active": category.is_active,
                "sort_order": category.sort_order,
                "created_at": category.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": category.updated_at.strftime("%Y-%m-%d %H:%M:%S") if category.updated_at else None
            })
        
        return success_response(
            message="获取商品种类列表成功",
            data={
                "categories": categories_data,
                "total": total,
                "page": page,
                "page_size": size
            }
        )
    except Exception as e:
        return error_response(message=f"获取商品种类列表失败: {str(e)}", code=500)


@router.get("/active", response_model=dict)
async def get_active_categories(db: Session = Depends(get_db)):
    """获取所有启用的商品种类（用于前端选择）"""
    try:
        categories = db.query(models.ProductCategory).filter(
            models.ProductCategory.is_active == True
        ).order_by(models.ProductCategory.sort_order, models.ProductCategory.name).all()
        
        categories_data = []
        for category in categories:
            categories_data.append({
                "id": category.id,
                "name": category.name,
                "display_name": category.display_name,
                "icon_url": category.icon_url
            })
        
        return success_response(
            message="获取启用商品种类成功",
            data={"categories": categories_data}
        )
    except Exception as e:
        return error_response(message=f"获取启用商品种类失败: {str(e)}", code=500)


@router.post("/", response_model=dict)
async def create_product_category(
    name: str = Form(...),
    display_name: str = Form(...),
    sort_order: int = Form(0),
    is_active: bool = Form(True),
    icon: Optional[UploadFile] = File(None),
    current_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """创建商品种类（管理员专用）"""
    try:
        # 检查名称是否已存在
        existing = db.query(models.ProductCategory).filter(
            models.ProductCategory.name == name
        ).first()
        if existing:
            return error_response(message="商品种类名称已存在", code=400)
        
        icon_url = None
        if icon:
            # 验证文件类型
            if not icon.content_type.startswith('image/'):
                return error_response(message="只能上传图片文件", code=400)
            
            # 生成唯一文件名
            file_extension = icon.filename.split('.')[-1] if '.' in icon.filename else 'jpg'
            unique_filename = f"{uuid.uuid4()}.{file_extension}"
            file_path = UPLOAD_DIR / unique_filename
            
            # 保存文件
            with open(file_path, "wb") as buffer:
                content = await icon.read()
                buffer.write(content)
            
            icon_url = f"/uploads/categories/{unique_filename}"
        
        # 创建商品种类
        category = models.ProductCategory(
            name=name,
            display_name=display_name,
            icon_url=icon_url,
            sort_order=sort_order,
            is_active=is_active
        )
        
        db.add(category)
        db.commit()
        db.refresh(category)
        
        return success_response(
            message="商品种类创建成功",
            data={
                "id": category.id,
                "name": category.name,
                "display_name": category.display_name,
                "icon_url": category.icon_url,
                "sort_order": category.sort_order,
                "is_active": category.is_active,
                "created_at": category.created_at.strftime("%Y-%m-%d %H:%M:%S")
            }
        )
    except Exception as e:
        db.rollback()
        return error_response(message=f"创建商品种类失败: {str(e)}", code=500)


@router.put("/{category_id}", response_model=dict)
async def update_product_category(
    category_id: int,
    name: Optional[str] = Form(None),
    display_name: Optional[str] = Form(None),
    sort_order: Optional[int] = Form(None),
    is_active: Optional[bool] = Form(None),
    icon: Optional[UploadFile] = File(None),
    current_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新商品种类（管理员专用）"""
    try:
        category = db.query(models.ProductCategory).filter(
            models.ProductCategory.id == category_id
        ).first()
        if not category:
            return error_response(message="商品种类不存在", code=404)
        
        # 检查名称冲突
        if name and name != category.name:
            existing = db.query(models.ProductCategory).filter(
                models.ProductCategory.name == name,
                models.ProductCategory.id != category_id
            ).first()
            if existing:
                return error_response(message="商品种类名称已存在", code=400)
        
        # 处理图标上传
        if icon:
            # 验证文件类型
            if not icon.content_type.startswith('image/'):
                return error_response(message="只能上传图片文件", code=400)
            
            # 删除旧图标
            if category.icon_url:
                old_file_path = Path(f"uploads{category.icon_url.replace('/uploads', '')}")
                if old_file_path.exists():
                    old_file_path.unlink()
            
            # 保存新图标
            file_extension = icon.filename.split('.')[-1] if '.' in icon.filename else 'jpg'
            unique_filename = f"{uuid.uuid4()}.{file_extension}"
            file_path = UPLOAD_DIR / unique_filename
            
            with open(file_path, "wb") as buffer:
                content = await icon.read()
                buffer.write(content)
            
            category.icon_url = f"/uploads/categories/{unique_filename}"
        
        # 更新字段
        if name is not None:
            category.name = name
        if display_name is not None:
            category.display_name = display_name
        if sort_order is not None:
            category.sort_order = sort_order
        if is_active is not None:
            category.is_active = is_active
        
        db.commit()
        db.refresh(category)
        
        return success_response(
            message="商品种类更新成功",
            data={
                "id": category.id,
                "name": category.name,
                "display_name": category.display_name,
                "icon_url": category.icon_url,
                "sort_order": category.sort_order,
                "is_active": category.is_active,
                "updated_at": category.updated_at.strftime("%Y-%m-%d %H:%M:%S") if category.updated_at else None
            }
        )
    except Exception as e:
        db.rollback()
        return error_response(message=f"更新商品种类失败: {str(e)}", code=500)


@router.delete("/{category_id}", response_model=dict)
async def delete_product_category(
    category_id: int,
    current_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """删除商品种类（管理员专用）"""
    try:
        category = db.query(models.ProductCategory).filter(
            models.ProductCategory.id == category_id
        ).first()
        if not category:
            return error_response(message="商品种类不存在", code=404)
        
        # 检查是否有商品使用此种类
        products_count = db.query(models.Product).filter(
            models.Product.category_id == category_id
        ).count()
        if products_count > 0:
            return error_response(message=f"无法删除，还有 {products_count} 个商品使用此种类", code=400)
        
        # 删除图标文件
        if category.icon_url:
            file_path = Path(f"uploads{category.icon_url.replace('/uploads', '')}")
            if file_path.exists():
                file_path.unlink()
        
        db.delete(category)
        db.commit()
        
        return success_response(message="商品种类删除成功")
    except Exception as e:
        db.rollback()
        return error_response(message=f"删除商品种类失败: {str(e)}", code=500)
