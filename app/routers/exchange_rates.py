from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from decimal import Decimal
from app.database import get_db
from app import models, schemas, auth
from app.response_utils import (
    success_response, error_response, not_found_response, forbidden_response
)

router = APIRouter()


@router.post("/")
async def create_exchange_rate(
    rate_data: schemas.ExchangeRateCreate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建或更新汇率（仅管理员）"""
    # 检查管理员权限
    if current_user.role != models.UserRole.ADMIN:
        return forbidden_response(message="需要管理员权限")

    try:
        # 检查是否已存在相同货币对的汇率
        existing_rate = db.query(models.ExchangeRate).filter(
            models.ExchangeRate.from_currency == rate_data.from_currency,
            models.ExchangeRate.to_currency == rate_data.to_currency,
            models.ExchangeRate.is_active == True
        ).first()

        if existing_rate:
            # 更新现有汇率
            existing_rate.rate = rate_data.rate
            db.commit()
            db.refresh(existing_rate)
            
            rate_dict = {
                "id": existing_rate.id,
                "from_currency": existing_rate.from_currency.value,
                "to_currency": existing_rate.to_currency.value,
                "rate": float(existing_rate.rate),
                "is_active": existing_rate.is_active,
                "created_at": existing_rate.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": existing_rate.updated_at.strftime("%Y-%m-%d %H:%M:%S") if existing_rate.updated_at else None
            }
            
            return success_response(
                message="汇率更新成功",
                data=rate_dict
            )
        else:
            # 创建新汇率
            db_rate = models.ExchangeRate(
                from_currency=rate_data.from_currency,
                to_currency=rate_data.to_currency,
                rate=rate_data.rate
            )
            db.add(db_rate)
            db.commit()
            db.refresh(db_rate)
            
            rate_dict = {
                "id": db_rate.id,
                "from_currency": db_rate.from_currency.value,
                "to_currency": db_rate.to_currency.value,
                "rate": float(db_rate.rate),
                "is_active": db_rate.is_active,
                "created_at": db_rate.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": None
            }
            
            return success_response(
                message="汇率创建成功",
                data=rate_dict
            )

    except Exception as e:
        db.rollback()
        return error_response(message=f"操作失败: {str(e)}")


@router.get("/")
async def get_exchange_rates(
    db: Session = Depends(get_db)
):
    """获取所有活跃汇率"""
    try:
        rates = db.query(models.ExchangeRate).filter(
            models.ExchangeRate.is_active == True
        ).all()
        
        rates_data = []
        for rate in rates:
            rates_data.append({
                "id": rate.id,
                "from_currency": rate.from_currency.value,
                "to_currency": rate.to_currency.value,
                "rate": float(rate.rate),
                "is_active": rate.is_active,
                "created_at": rate.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": rate.updated_at.strftime("%Y-%m-%d %H:%M:%S") if rate.updated_at else None
            })
        
        return success_response(
            message="获取汇率列表成功",
            data={"rates": rates_data, "total": len(rates_data)}
        )

    except Exception as e:
        return error_response(message=f"获取汇率失败: {str(e)}")


@router.get("/{from_currency}/{to_currency}")
async def get_exchange_rate(
    from_currency: str,
    to_currency: str,
    db: Session = Depends(get_db)
):
    """获取指定货币对的汇率"""
    try:
        # 验证货币类型
        try:
            from_curr = models.CurrencyType(from_currency)
            to_curr = models.CurrencyType(to_currency)
        except ValueError:
            return error_response(message="不支持的货币类型")

        rate = db.query(models.ExchangeRate).filter(
            models.ExchangeRate.from_currency == from_curr,
            models.ExchangeRate.to_currency == to_curr,
            models.ExchangeRate.is_active == True
        ).first()

        if not rate:
            return not_found_response(message=f"未找到 {from_currency} 到 {to_currency} 的汇率")

        rate_data = {
            "id": rate.id,
            "from_currency": rate.from_currency.value,
            "to_currency": rate.to_currency.value,
            "rate": float(rate.rate),
            "is_active": rate.is_active,
            "created_at": rate.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": rate.updated_at.strftime("%Y-%m-%d %H:%M:%S") if rate.updated_at else None
        }

        return success_response(
            message="获取汇率成功",
            data=rate_data
        )

    except Exception as e:
        return error_response(message=f"获取汇率失败: {str(e)}")


def calculate_converted_amount(amount: Decimal, from_currency: models.CurrencyType, to_currency: models.CurrencyType, db: Session) -> Decimal:
    """计算货币转换后的金额"""
    if from_currency == to_currency:
        return amount
    
    # 查找汇率
    rate = db.query(models.ExchangeRate).filter(
        models.ExchangeRate.from_currency == from_currency,
        models.ExchangeRate.to_currency == to_currency,
        models.ExchangeRate.is_active == True
    ).first()
    
    if not rate:
        raise ValueError(f"未找到 {from_currency.value} 到 {to_currency.value} 的汇率")
    
    return amount * rate.rate
