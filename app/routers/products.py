from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app import models, schemas, auth
from app.response_utils import (
    success_response, error_response, create_response, update_response,
    list_response, not_found_response, forbidden_response,
    StatusCode, ResponseMessage
)

router = APIRouter()


@router.post("/")
async def create_product(
    product: schemas.ProductCreate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建礼品卡回收商品（仅管理员）"""
    # 检查管理员权限
    if current_user.role != models.UserRole.ADMIN:
        return forbidden_response(message="需要管理员权限")

    try:
        db_product = models.Product(
            name=product.name,
            description=product.description,
            min_amount=product.min_amount,
            max_amount=product.max_amount,
            currency=product.currency,
            avatar_url=product.avatar_url,
            category=product.category,
            average_time_minutes=product.average_time_minutes
        )
        db.add(db_product)
        db.commit()
        db.refresh(db_product)

        product_data = {
            "id": db_product.id,
            "name": db_product.name,
            "description": db_product.description,
            "min_amount": float(db_product.min_amount),
            "max_amount": float(db_product.max_amount),
            "currency": db_product.currency.value,
            "avatar_url": db_product.avatar_url,
            "category": db_product.category,
            "average_time_minutes": db_product.average_time_minutes,
            "transaction_count": db_product.transaction_count,
            "like_count": db_product.like_count,
            "success_rate": float(db_product.success_rate),
            "is_available": db_product.is_available,
            "created_at": db_product.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

        return success_response(
            message="礼品卡回收商品创建成功",
            data=product_data
        )

    except Exception as e:
        db.rollback()
        return error_response(
            message=f"创建商品失败: {str(e)}"
        )


@router.get("/")
async def list_products(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    search: Optional[str] = None,
    available_only: bool = True,
    db: Session = Depends(get_db)
):
    """获取礼品卡回收商品列表"""
    try:
        query = db.query(models.Product)

        if available_only:
            query = query.filter(models.Product.is_available == True)

        if category:
            query = query.filter(models.Product.category == category)

        if search:
            query = query.filter(
                models.Product.name.contains(search) |
                models.Product.description.contains(search)
            )

        # 获取总数
        total = query.count()

        # 获取商品列表，按交易量和点赞数排序
        products = query.order_by(
            models.Product.transaction_count.desc(),
            models.Product.like_count.desc(),
            models.Product.created_at.desc()
        ).offset(skip).limit(limit).all()

        # 转换为字典格式
        products_data = []
        for product in products:
            products_data.append({
                "id": product.id,
                "name": product.name,
                "avatar_url": product.avatar_url,
                "description": product.description,
                "min_amount": float(product.min_amount),
                "max_amount": float(product.max_amount),
                "currency": product.currency.value,
                "category": product.category,
                "average_time_minutes": product.average_time_minutes,
                "transaction_count": product.transaction_count,
                "like_count": product.like_count,
                "success_rate": float(product.success_rate),
                "is_available": product.is_available,
                "created_at": product.created_at.strftime("%Y-%m-%d %H:%M:%S")
            })

        return success_response(
            message="获取礼品卡回收商品列表成功",
            data={
                "products": products_data,
                "total": total,
                "page": skip // limit + 1 if limit > 0 else 1,
                "page_size": limit
            }
        )

    except Exception as e:
        return error_response(
            message=f"获取商品列表失败: {str(e)}"
        )


@router.get("/categories")
async def get_categories(db: Session = Depends(get_db)):
    """获取商品分类列表"""
    try:
        categories = db.query(models.Product.category).distinct().all()
        category_list = [cat[0] for cat in categories if cat[0]]

        return success_response(
            message="获取分类列表成功",
            data={"categories": category_list}
        )
    except Exception as e:
        return error_response(
            message=f"获取分类列表失败: {str(e)}"
        )


@router.get("/{product_id}")
async def get_product(product_id: int, db: Session = Depends(get_db)):
    """获取商品详情"""
    try:
        product = db.query(models.Product).filter(models.Product.id == product_id).first()
        if not product:
            return not_found_response(message="商品不存在")

        product_data = {
            "id": product.id,
            "name": product.name,
            "avatar_url": product.avatar_url,
            "description": product.description,
            "min_amount": float(product.min_amount),
            "max_amount": float(product.max_amount),
            "currency": product.currency.value,
            "category": product.category,
            "average_time_minutes": product.average_time_minutes,
            "transaction_count": product.transaction_count,
            "like_count": product.like_count,
            "success_rate": float(product.success_rate),
            "is_available": product.is_available,
            "created_at": product.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

        return success_response(
            message="获取商品详情成功",
            data=product_data
        )

    except Exception as e:
        return error_response(
            message=f"获取商品详情失败: {str(e)}"
        )


@router.put("/{product_id}")
async def update_product(
    product_id: int,
    product_update: schemas.ProductUpdate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新商品信息（仅管理员）"""
    # 检查管理员权限
    if current_user.role != models.UserRole.ADMIN:
        return forbidden_response(message="需要管理员权限")

    try:
        product = db.query(models.Product).filter(models.Product.id == product_id).first()
        if not product:
            return not_found_response(message="商品不存在")

        # 更新字段
        for field, value in product_update.dict(exclude_unset=True).items():
            setattr(product, field, value)

        db.commit()
        db.refresh(product)

        product_data = {
            "id": product.id,
            "name": product.name,
            "avatar_url": product.avatar_url,
            "description": product.description,
            "min_amount": float(product.min_amount),
            "max_amount": float(product.max_amount),
            "currency": product.currency.value,
            "category": product.category,
            "average_time_minutes": product.average_time_minutes,
            "transaction_count": product.transaction_count,
            "like_count": product.like_count,
            "success_rate": float(product.success_rate),
            "is_available": product.is_available,
            "created_at": product.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

        return success_response(
            message="商品更新成功",
            data=product_data
        )

    except Exception as e:
        db.rollback()
        return error_response(
            message=f"更新商品失败: {str(e)}"
        )


@router.delete("/{product_id}")
async def delete_product(
    product_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除商品（仅管理员）"""
    # 检查管理员权限
    if current_user.role != models.UserRole.ADMIN:
        return forbidden_response(message="需要管理员权限")

    try:
        product = db.query(models.Product).filter(models.Product.id == product_id).first()
        if not product:
            return not_found_response(message="商品不存在")

        # 检查是否有进行中的交易
        active_transactions = db.query(models.Transaction).filter(
            models.Transaction.product_id == product_id,
            models.Transaction.status.in_([models.TransactionStatus.PENDING, models.TransactionStatus.CONFIRMED])
        ).first()

        if active_transactions:
            return error_response(message="商品有进行中的交易，无法删除")

        db.delete(product)
        db.commit()

        return success_response(message="商品删除成功")

    except Exception as e:
        db.rollback()
        return error_response(
            message=f"删除商品失败: {str(e)}"
        )
