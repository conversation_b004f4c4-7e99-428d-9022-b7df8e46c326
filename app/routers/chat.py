from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.database import get_db
from app import models, schemas, auth
from app.websocket_manager import manager
from app.response_utils import success_response, StatusCode
import json
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


async def get_user_from_token(token: str, db: Session):
    """从 token 获取用户信息"""
    try:
        user = await auth.get_current_user(token, db)
        return user
    except Exception as e:
        logger.error(f"Token 验证失败: {e}")
        return None


@router.websocket("/ws/{user_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    user_id: int,
    token: str = Query(...),
    db: Session = Depends(get_db)
):
    """WebSocket 聊天端点"""
    # 验证用户身份
    user = await get_user_from_token(token, db)
    if not user or user.id != user_id:
        await websocket.close(code=4001, reason="身份验证失败")
        return
    
    # 准备用户信息
    user_info = {
        "username": user.username,
        "full_name": user.full_name,
        "avatar_url": user.avatar_url
    }
    
    # 建立连接
    await manager.connect(websocket, user_id, user_info)
    
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            try:
                message_data = json.loads(data)
                await handle_websocket_message(user_id, message_data, db)
            except json.JSONDecodeError:
                logger.error(f"用户 {user_id} 发送了无效的 JSON 数据: {data}")
                await manager.send_personal_message(
                    json.dumps({"type": "error", "message": "无效的消息格式"}),
                    user_id
                )
    except WebSocketDisconnect:
        manager.disconnect(user_id)
        # 通知其他用户该用户已下线
        await manager.broadcast_user_status(user_id, "offline")


async def handle_websocket_message(user_id: int, message_data: dict, db: Session):
    """处理 WebSocket 消息"""
    message_type = message_data.get("type")
    
    if message_type == "chat_message":
        # 处理聊天消息
        receiver_id = message_data.get("receiver_id")
        content = message_data.get("content")
        
        if not receiver_id or not content:
            await manager.send_personal_message(
                json.dumps({"type": "error", "message": "消息格式不完整"}),
                user_id
            )
            return
        
        # 检查接收者是否存在
        receiver = db.query(models.User).filter(models.User.id == receiver_id).first()
        if not receiver:
            await manager.send_personal_message(
                json.dumps({"type": "error", "message": "接收者不存在"}),
                user_id
            )
            return
        
        # 保存消息到数据库
        db_message = models.Message(
            sender_id=user_id,
            receiver_id=receiver_id,
            content=content,
            message_type=models.MessageType.CHAT
        )
        db.add(db_message)
        db.commit()
        
        # 发送消息给接收者
        await manager.send_message_to_user(user_id, receiver_id, {"content": content})
        
    elif message_type == "get_online_users":
        # 获取在线用户列表
        await manager.get_online_users(user_id)
        
    elif message_type == "ping":
        # 心跳检测
        await manager.send_personal_message(
            json.dumps({"type": "pong", "timestamp": message_data.get("timestamp")}),
            user_id
        )
    
    else:
        await manager.send_personal_message(
            json.dumps({"type": "error", "message": f"未知的消息类型: {message_type}"}),
            user_id
        )


@router.get("/online-users")
async def get_online_users(current_user: models.User = Depends(auth.get_current_active_user)):
    """获取在线用户列表（HTTP 接口）"""
    online_users = []
    for user_id, user_info in manager.user_info.items():
        if user_id != current_user.id:
            online_users.append({
                "user_id": user_id,
                "username": user_info.get("username"),
                "full_name": user_info.get("full_name"),
                "avatar_url": user_info.get("avatar_url")
            })

    return success_response(data={"online_users": online_users}, message="获取在线用户列表成功")


@router.get("/user-status/{user_id}")
async def check_user_status(
    user_id: int,
    current_user: models.User = Depends(auth.get_current_active_user)
):
    """检查用户在线状态"""
    is_online = manager.is_user_online(user_id)
    return success_response(
        data={"user_id": user_id, "is_online": is_online},
        message="获取用户状态成功"
    )
