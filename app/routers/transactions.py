from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app import models, schemas, auth
from app.response_utils import (
    success_response, error_response, create_response, update_response,
    list_response, not_found_response, forbidden_response,
    StatusCode, ResponseMessage
)

router = APIRouter()


@router.post("/", response_model=schemas.Transaction)
async def create_transaction(
    transaction: schemas.TransactionCreate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建交易"""
    # 检查商品是否存在
    product = db.query(models.Product).filter(models.Product.id == transaction.product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="商品不存在")
    
    # 检查商品是否可用
    if not product.is_available:
        raise HTTPException(status_code=400, detail="商品不可用")
    
    # 不能购买自己的商品
    if product.owner_id == current_user.id:
        raise HTTPException(status_code=400, detail="不能购买自己的商品")
    
    # 检查是否已有进行中的交易
    existing_transaction = db.query(models.Transaction).filter(
        models.Transaction.product_id == transaction.product_id,
        models.Transaction.buyer_id == current_user.id,
        models.Transaction.status.in_([models.TransactionStatus.PENDING, models.TransactionStatus.CONFIRMED])
    ).first()
    
    if existing_transaction:
        raise HTTPException(status_code=400, detail="已有进行中的交易")
    
    db_transaction = models.Transaction(
        product_id=transaction.product_id,
        buyer_id=current_user.id,
        seller_id=product.owner_id,
        amount=transaction.amount,
        notes=transaction.notes
    )
    db.add(db_transaction)
    db.commit()
    db.refresh(db_transaction)
    
    return db_transaction


@router.get("/", response_model=List[schemas.Transaction])
async def list_transactions(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    as_buyer: Optional[bool] = None,
    as_seller: Optional[bool] = None,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取交易列表"""
    query = db.query(models.Transaction)
    
    # 根据角色过滤
    if as_buyer is True:
        query = query.filter(models.Transaction.buyer_id == current_user.id)
    elif as_seller is True:
        query = query.filter(models.Transaction.seller_id == current_user.id)
    else:
        # 默认显示所有相关交易
        query = query.filter(
            (models.Transaction.buyer_id == current_user.id) |
            (models.Transaction.seller_id == current_user.id)
        )
    
    if status:
        query = query.filter(models.Transaction.status == status)
    
    transactions = query.order_by(models.Transaction.created_at.desc()).offset(skip).limit(limit).all()
    return transactions


@router.get("/as-buyer", response_model=List[schemas.Transaction])
async def list_buyer_transactions(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取作为买家的交易列表"""
    transactions = db.query(models.Transaction).filter(
        models.Transaction.buyer_id == current_user.id
    ).order_by(models.Transaction.created_at.desc()).offset(skip).limit(limit).all()
    
    return transactions


@router.get("/as-seller", response_model=List[schemas.Transaction])
async def list_seller_transactions(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取作为卖家的交易列表"""
    transactions = db.query(models.Transaction).filter(
        models.Transaction.seller_id == current_user.id
    ).order_by(models.Transaction.created_at.desc()).offset(skip).limit(limit).all()
    
    return transactions


@router.get("/{transaction_id}", response_model=schemas.Transaction)
async def get_transaction(
    transaction_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取交易详情"""
    transaction = db.query(models.Transaction).filter(models.Transaction.id == transaction_id).first()
    if not transaction:
        raise HTTPException(status_code=404, detail="交易不存在")
    
    # 检查权限
    if transaction.buyer_id != current_user.id and transaction.seller_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此交易")
    
    return transaction


@router.put("/{transaction_id}", response_model=schemas.Transaction)
async def update_transaction(
    transaction_id: int,
    transaction_update: schemas.TransactionUpdate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新交易状态"""
    transaction = db.query(models.Transaction).filter(models.Transaction.id == transaction_id).first()
    if not transaction:
        raise HTTPException(status_code=404, detail="交易不存在")
    
    # 检查权限
    if transaction.buyer_id != current_user.id and transaction.seller_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权修改此交易")
    
    # 状态转换逻辑
    if transaction_update.status:
        current_status = transaction.status
        new_status = transaction_update.status
        
        # 买家可以取消待处理的交易
        if (current_user.id == transaction.buyer_id and 
            current_status == models.TransactionStatus.PENDING and 
            new_status == models.TransactionStatus.CANCELLED):
            transaction.status = new_status
        
        # 卖家可以确认待处理的交易
        elif (current_user.id == transaction.seller_id and 
              current_status == models.TransactionStatus.PENDING and 
              new_status == models.TransactionStatus.CONFIRMED):
            transaction.status = new_status
        
        # 双方都可以完成已确认的交易
        elif (current_status == models.TransactionStatus.CONFIRMED and 
              new_status == models.TransactionStatus.COMPLETED):
            transaction.status = new_status
            # 交易完成后，商品设为不可用
            product = db.query(models.Product).filter(models.Product.id == transaction.product_id).first()
            if product:
                product.is_available = False
        
        else:
            raise HTTPException(status_code=400, detail="无效的状态转换")
    
    # 更新备注
    if transaction_update.notes is not None:
        transaction.notes = transaction_update.notes
    
    db.commit()
    db.refresh(transaction)
    
    return transaction


@router.delete("/{transaction_id}")
async def cancel_transaction(
    transaction_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """取消交易"""
    transaction = db.query(models.Transaction).filter(models.Transaction.id == transaction_id).first()
    if not transaction:
        raise HTTPException(status_code=404, detail="交易不存在")
    
    # 只有买家可以取消待处理的交易
    if (transaction.buyer_id != current_user.id or 
        transaction.status != models.TransactionStatus.PENDING):
        raise HTTPException(status_code=403, detail="无法取消此交易")
    
    transaction.status = models.TransactionStatus.CANCELLED
    db.commit()
    
    return {"message": "交易已取消"}
