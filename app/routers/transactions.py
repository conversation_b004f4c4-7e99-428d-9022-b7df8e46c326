from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import uuid
from datetime import datetime
from decimal import Decimal
from app.database import get_db
from app import models, schemas, auth
from app.response_utils import (
    success_response, error_response, create_response, update_response,
    list_response, not_found_response, forbidden_response,
    StatusCode, ResponseMessage
)
from app.routers.exchange_rates import calculate_converted_amount

router = APIRouter()


@router.post("/sell-with-image")
async def create_sell_transaction_with_image(
    product_id: int = Form(...),
    amount: float = Form(...),
    currency: str = Form("USD"),
    notes: Optional[str] = Form(None),
    gift_card_info: Optional[str] = Form(None),
    image: UploadFile = File(...),
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """卖出礼品卡（包含图片上传）"""
    try:
        # 验证商品是否存在且可用
        product = db.query(models.Product).filter(
            models.Product.id == product_id,
            models.Product.is_available == True
        ).first()

        if not product:
            return error_response(message="商品不存在或不可用")

        # 转换金额为Decimal
        amount_decimal = Decimal(str(amount))

        # 验证金额是否在商品接收范围内
        if amount_decimal < product.min_amount or amount_decimal > product.max_amount:
            return error_response(
                message=f"金额必须在 {product.min_amount} - {product.max_amount} {product.currency.value} 之间"
            )

        # 验证货币类型
        try:
            currency_enum = models.CurrencyType(currency)
        except ValueError:
            return error_response(message="不支持的货币类型")

        # 验证图片文件
        if not image.filename:
            return error_response(message="请上传礼品卡图片")

        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
        if image.content_type not in allowed_types:
            return error_response(message="不支持的文件类型，请上传图片文件")

        # 创建上传目录
        upload_dir = "uploads/transaction_images"
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一文件名
        file_extension = os.path.splitext(image.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)

        # 保存图片文件
        with open(file_path, "wb") as buffer:
            content = await image.read()
            buffer.write(content)

        # 创建卖出交易记录
        db_transaction = models.Transaction(
            product_id=product_id,
            buyer_id=None,    # 卖出时暂无买家
            seller_id=current_user.id,
            amount=amount_decimal,
            currency=currency_enum,
            transaction_type=models.TransactionType.SELL,
            status=models.TransactionStatus.PENDING,
            notes=notes,
            gift_card_info=gift_card_info
        )
        db.add(db_transaction)

        # 更新商品交易统计
        product.transaction_count += 1

        db.commit()
        db.refresh(db_transaction)

        # 创建交易图片记录
        db_image = models.TransactionImage(
            transaction_id=db_transaction.id,
            image_url=f"/uploads/transaction_images/{unique_filename}",
            image_type="gift_card"
        )
        db.add(db_image)
        db.commit()
        db.refresh(db_image)

        # 构造响应数据
        transaction_data = {
            "id": db_transaction.id,
            "product_id": db_transaction.product_id,
            "amount": float(db_transaction.amount),
            "currency": db_transaction.currency.value,
            "transaction_type": db_transaction.transaction_type.value,
            "status": db_transaction.status.value,
            "notes": db_transaction.notes,
            "gift_card_info": db_transaction.gift_card_info,
            "seller_id": db_transaction.seller_id,
            "image_url": db_image.image_url,
            "created_at": db_transaction.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

        return success_response(
            message="卖出交易创建成功，图片上传完成",
            data=transaction_data
        )

    except Exception as e:
        db.rollback()
        # 如果出错，删除已上传的图片文件
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except:
                pass
        return error_response(
            message=f"创建卖出交易失败: {str(e)}"
        )


@router.post("/sell")
async def create_sell_transaction(
    transaction: schemas.TransactionCreate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """卖出礼品卡（仅创建交易，需要后续上传图片）"""
    try:
        # 验证商品是否存在且可用
        product = db.query(models.Product).filter(
            models.Product.id == transaction.product_id,
            models.Product.is_available == True
        ).first()

        if not product:
            return error_response(message="商品不存在或不可用")

        # 验证金额是否在商品接收范围内
        if transaction.amount < product.min_amount or transaction.amount > product.max_amount:
            return error_response(
                message=f"金额必须在 {product.min_amount} - {product.max_amount} {product.currency.value} 之间"
            )

        # 创建卖出交易记录
        db_transaction = models.Transaction(
            product_id=transaction.product_id,
            buyer_id=None,    # 卖出时暂无买家
            seller_id=current_user.id,
            amount=transaction.amount,
            currency=transaction.currency,
            transaction_type=models.TransactionType.SELL,
            status=models.TransactionStatus.PENDING,  # 待验证状态
            notes=transaction.notes,
            gift_card_info=transaction.gift_card_info
        )

        db.add(db_transaction)

        # 更新商品交易统计
        product.transaction_count += 1

        db.commit()
        db.refresh(db_transaction)

        # 如果提供了图片URL，创建图片记录
        if transaction.image_url:
            db_image = models.TransactionImage(
                transaction_id=db_transaction.id,
                image_url=transaction.image_url,
                image_type="gift_card"
            )
            db.add(db_image)
            db.commit()

        # 构造响应数据
        transaction_data = {
            "id": db_transaction.id,
            "product_id": db_transaction.product_id,
            "amount": float(db_transaction.amount),
            "currency": db_transaction.currency.value,
            "transaction_type": db_transaction.transaction_type.value,
            "status": db_transaction.status.value,
            "notes": db_transaction.notes,
            "gift_card_info": db_transaction.gift_card_info,
            "seller_id": db_transaction.seller_id,
            "image_url": transaction.image_url,
            "created_at": db_transaction.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

        message = "卖出交易创建成功，状态为待验证"
        if transaction.image_url:
            message += "，图片已关联"
        else:
            message += "，请上传礼品卡图片"

        return success_response(
            message=message,
            data=transaction_data
        )

    except Exception as e:
        db.rollback()
        return error_response(
            message=f"创建卖出交易失败: {str(e)}"
        )


@router.post("/upload-image/{transaction_id}")
async def upload_transaction_image(
    transaction_id: int,
    file: UploadFile = File(...),
    image_type: str = "gift_card",
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """上传交易相关图片"""
    try:
        # 检查交易是否存在且属于当前用户
        transaction = db.query(models.Transaction).filter(
            models.Transaction.id == transaction_id,
            models.Transaction.seller_id == current_user.id
        ).first()

        if not transaction:
            return not_found_response(message="交易不存在或无权限")

        # 检查文件类型
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
        if file.content_type not in allowed_types:
            return error_response(message="不支持的文件类型，请上传图片文件")

        # 创建上传目录
        upload_dir = "uploads/transaction_images"
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)

        # 保存文件
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 保存图片记录到数据库
        db_image = models.TransactionImage(
            transaction_id=transaction_id,
            image_url=file_path,
            image_type=image_type
        )
        db.add(db_image)
        db.commit()
        db.refresh(db_image)

        # 构造响应数据
        image_data = {
            "id": db_image.id,
            "transaction_id": db_image.transaction_id,
            "image_url": db_image.image_url,
            "image_type": db_image.image_type,
            "created_at": db_image.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

        return success_response(
            message="图片上传成功",
            data=image_data
        )

    except Exception as e:
        return error_response(
            message=f"图片上传失败: {str(e)}"
        )


@router.get("/", response_model=List[schemas.Transaction])
async def list_transactions(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    as_buyer: Optional[bool] = None,
    as_seller: Optional[bool] = None,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取交易列表"""
    query = db.query(models.Transaction)
    
    # 根据角色过滤
    if as_buyer is True:
        query = query.filter(models.Transaction.buyer_id == current_user.id)
    elif as_seller is True:
        query = query.filter(models.Transaction.seller_id == current_user.id)
    else:
        # 默认显示所有相关交易
        query = query.filter(
            (models.Transaction.buyer_id == current_user.id) |
            (models.Transaction.seller_id == current_user.id)
        )
    
    if status:
        query = query.filter(models.Transaction.status == status)
    
    transactions = query.order_by(models.Transaction.created_at.desc()).offset(skip).limit(limit).all()
    return transactions


@router.get("/as-buyer", response_model=List[schemas.Transaction])
async def list_buyer_transactions(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取作为买家的交易列表"""
    transactions = db.query(models.Transaction).filter(
        models.Transaction.buyer_id == current_user.id
    ).order_by(models.Transaction.created_at.desc()).offset(skip).limit(limit).all()
    
    return transactions


@router.get("/as-seller", response_model=List[schemas.Transaction])
async def list_seller_transactions(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取作为卖家的交易列表"""
    transactions = db.query(models.Transaction).filter(
        models.Transaction.seller_id == current_user.id
    ).order_by(models.Transaction.created_at.desc()).offset(skip).limit(limit).all()
    
    return transactions


@router.get("/{transaction_id}")
async def get_transaction(
    transaction_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取交易详情"""
    transaction = db.query(models.Transaction).filter(models.Transaction.id == transaction_id).first()
    if not transaction:
        return not_found_response(message="交易不存在")

    # 检查权限（管理员可以查看所有交易）
    if (current_user.role != models.UserRole.ADMIN and
        transaction.buyer_id != current_user.id and
        transaction.seller_id != current_user.id):
        return forbidden_response(message="无权访问此交易")

    # 获取交易关联的图片
    images = db.query(models.TransactionImage).filter(
        models.TransactionImage.transaction_id == transaction_id
    ).all()

    # 构造响应数据
    transaction_data = {
        "id": transaction.id,
        "product_id": transaction.product_id,
        "buyer_id": transaction.buyer_id,
        "seller_id": transaction.seller_id,
        "amount": float(transaction.amount),
        "currency": transaction.currency.value,
        "transaction_type": transaction.transaction_type.value,
        "status": transaction.status.value,
        "notes": transaction.notes,
        "gift_card_info": transaction.gift_card_info,
        "images": [{"id": img.id, "url": img.image_url, "type": img.image_type} for img in images],
        "created_at": transaction.created_at.strftime("%Y-%m-%d %H:%M:%S")
    }

    return success_response(
        message="获取交易详情成功",
        data=transaction_data
    )


@router.put("/{transaction_id}", response_model=schemas.Transaction)
async def update_transaction(
    transaction_id: int,
    transaction_update: schemas.TransactionUpdate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新交易状态"""
    transaction = db.query(models.Transaction).filter(models.Transaction.id == transaction_id).first()
    if not transaction:
        raise HTTPException(status_code=404, detail="交易不存在")
    
    # 检查权限
    if transaction.buyer_id != current_user.id and transaction.seller_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权修改此交易")
    
    # 状态转换逻辑
    if transaction_update.status:
        current_status = transaction.status
        new_status = transaction_update.status
        
        # 买家可以取消待处理的交易
        if (current_user.id == transaction.buyer_id and 
            current_status == models.TransactionStatus.PENDING and 
            new_status == models.TransactionStatus.CANCELLED):
            transaction.status = new_status
        
        # 卖家可以确认待处理的交易
        elif (current_user.id == transaction.seller_id and 
              current_status == models.TransactionStatus.PENDING and 
              new_status == models.TransactionStatus.CONFIRMED):
            transaction.status = new_status
        
        # 双方都可以完成已确认的交易
        elif (current_status == models.TransactionStatus.CONFIRMED and 
              new_status == models.TransactionStatus.COMPLETED):
            transaction.status = new_status
            # 交易完成后，商品设为不可用
            product = db.query(models.Product).filter(models.Product.id == transaction.product_id).first()
            if product:
                product.is_available = False
        
        else:
            raise HTTPException(status_code=400, detail="无效的状态转换")
    
    # 更新备注
    if transaction_update.notes is not None:
        transaction.notes = transaction_update.notes
    
    db.commit()
    db.refresh(transaction)
    
    return transaction


@router.delete("/{transaction_id}")
async def cancel_transaction(
    transaction_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """取消交易"""
    transaction = db.query(models.Transaction).filter(models.Transaction.id == transaction_id).first()
    if not transaction:
        raise HTTPException(status_code=404, detail="交易不存在")
    
    # 只有买家可以取消待处理的交易
    if (transaction.buyer_id != current_user.id or 
        transaction.status != models.TransactionStatus.PENDING):
        raise HTTPException(status_code=403, detail="无法取消此交易")
    
    transaction.status = models.TransactionStatus.CANCELLED
    db.commit()
    
    return {"message": "交易已取消"}


@router.put("/{transaction_id}/approve")
async def approve_transaction(
    transaction_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """管理员审核通过交易"""
    # 检查管理员权限
    if current_user.role != models.UserRole.ADMIN:
        return forbidden_response(message="需要管理员权限")

    try:
        transaction = db.query(models.Transaction).filter(
            models.Transaction.id == transaction_id
        ).first()

        if not transaction:
            return not_found_response(message="交易不存在")

        if transaction.status != models.TransactionStatus.PENDING:
            return error_response(message="只能审核待验证状态的交易")

        # 获取卖家信息
        seller = db.query(models.User).filter(models.User.id == transaction.seller_id).first()
        if not seller:
            return error_response(message="卖家不存在")

        # 计算应该给用户增加的余额
        # 根据用户的默认货币和汇率计算
        try:
            if seller.default_currency == transaction.currency:
                # 货币相同，直接增加
                converted_amount = transaction.amount
            else:
                # 需要汇率转换
                converted_amount = calculate_converted_amount(
                    transaction.amount,
                    transaction.currency,
                    seller.default_currency,
                    db
                )

            # 增加用户余额
            if seller.default_currency == models.CurrencyType.USD:
                seller.balance_usd += converted_amount
            elif seller.default_currency == models.CurrencyType.NGN:
                seller.balance_ngn += converted_amount

            # 更新交易状态
            transaction.status = models.TransactionStatus.COMPLETED

            db.commit()
            db.refresh(transaction)
            db.refresh(seller)

            # 构造响应数据
            transaction_data = {
                "id": transaction.id,
                "product_id": transaction.product_id,
                "amount": float(transaction.amount),
                "currency": transaction.currency.value,
                "status": transaction.status.value,
                "seller_id": transaction.seller_id,
                "converted_amount": float(converted_amount),
                "seller_currency": seller.default_currency.value,
                "seller_new_balance": float(seller.balance_usd if seller.default_currency == models.CurrencyType.USD else seller.balance_ngn),
                "created_at": transaction.created_at.strftime("%Y-%m-%d %H:%M:%S")
            }

            return success_response(
                message=f"交易审核通过，已为用户增加 {converted_amount} {seller.default_currency.value} 余额",
                data=transaction_data
            )

        except ValueError as e:
            return error_response(message=f"汇率转换失败: {str(e)}")

    except Exception as e:
        db.rollback()
        return error_response(message=f"审核失败: {str(e)}")


@router.put("/{transaction_id}/reject")
async def reject_transaction(
    transaction_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """管理员拒绝交易"""
    # 检查管理员权限
    if current_user.role != models.UserRole.ADMIN:
        return forbidden_response(message="需要管理员权限")

    try:
        transaction = db.query(models.Transaction).filter(
            models.Transaction.id == transaction_id
        ).first()

        if not transaction:
            return not_found_response(message="交易不存在")

        if transaction.status != models.TransactionStatus.PENDING:
            return error_response(message="只能拒绝待验证状态的交易")

        # 更新交易状态
        transaction.status = models.TransactionStatus.CANCELLED

        db.commit()
        db.refresh(transaction)

        # 构造响应数据
        transaction_data = {
            "id": transaction.id,
            "product_id": transaction.product_id,
            "amount": float(transaction.amount),
            "currency": transaction.currency.value,
            "status": transaction.status.value,
            "seller_id": transaction.seller_id,
            "created_at": transaction.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }

        return success_response(
            message="交易已拒绝",
            data=transaction_data
        )

    except Exception as e:
        db.rollback()
        return error_response(message=f"拒绝失败: {str(e)}")
