from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from datetime import datetime, timedelta
from typing import List, Optional
from app.database import get_db
from app import models, schemas, auth
from app.response_utils import (
    success_response, error_response, list_response,
    StatusCode, ResponseMessage
)

router = APIRouter()

def get_admin_user(current_user: models.User = Depends(auth.get_current_active_user)):
    """验证管理员权限"""
    if current_user.role != models.UserRole.ADMIN:
        raise HTTPException(
            status_code=403,
            detail="只有管理员可以访问此接口"
        )
    return current_user

# 仪表板相关接口
@router.get("/dashboard/stats")
async def get_dashboard_stats(
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取仪表板统计数据"""
    # 用户统计
    total_users = db.query(models.User).count()
    active_users = db.query(models.User).filter(models.User.is_active == True).count()
    admin_users = db.query(models.User).filter(models.User.role == models.UserRole.ADMIN).count()
    
    # 今日新用户
    today = datetime.now().date()
    new_users_today = db.query(models.User).filter(
        func.date(models.User.created_at) == today
    ).count()
    
    # 商品统计
    total_products = db.query(models.Product).count()
    available_products = db.query(models.Product).filter(models.Product.is_available == True).count()
    
    # 今日新商品
    new_products_today = db.query(models.Product).filter(
        func.date(models.Product.created_at) == today
    ).count()
    
    # 交易统计
    total_transactions = db.query(models.Transaction).count()
    completed_transactions = db.query(models.Transaction).filter(
        models.Transaction.status == models.TransactionStatus.COMPLETED
    ).count()
    
    # 今日交易
    transactions_today = db.query(models.Transaction).filter(
        func.date(models.Transaction.created_at) == today
    ).count()
    
    # 今日交易金额
    today_amount = db.query(func.sum(models.Transaction.amount)).filter(
        and_(
            func.date(models.Transaction.created_at) == today,
            models.Transaction.status == models.TransactionStatus.COMPLETED
        )
    ).scalar() or 0
    
    stats = {
        "users": {
            "total": total_users,
            "active": active_users,
            "admin": admin_users,
            "new_today": new_users_today
        },
        "products": {
            "total": total_products,
            "available": available_products,
            "new_today": new_products_today
        },
        "transactions": {
            "total": total_transactions,
            "completed": completed_transactions,
            "today_count": transactions_today,
            "today_amount": float(today_amount)
        }
    }
    
    return success_response(data=stats, message="获取统计数据成功")

@router.get("/dashboard/recent-transactions")
async def get_recent_transactions(
    limit: int = Query(10, ge=1, le=50),
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取最近交易"""
    transactions = db.query(models.Transaction).order_by(
        models.Transaction.created_at.desc()
    ).limit(limit).all()
    
    transactions_data = []
    for transaction in transactions:
        transactions_data.append({
            "id": transaction.id,
            "amount": float(transaction.amount),
            "status": transaction.status.value,
            "buyer_id": transaction.buyer_id,
            "seller_id": transaction.seller_id,
            "product_id": transaction.product_id,
            "created_at": transaction.created_at.isoformat()
        })
    
    return success_response(data=transactions_data, message="获取最近交易成功")

@router.get("/dashboard/system-health")
async def get_system_health(
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取系统健康状态"""
    try:
        # 检查数据库连接
        db.execute("SELECT 1")
        db_status = "healthy"
    except Exception:
        db_status = "error"
    
    # 检查各模块状态
    health_data = {
        "database": db_status,
        "api": "healthy",
        "websocket": "healthy",
        "timestamp": datetime.now().isoformat()
    }
    
    return success_response(data=health_data, message="获取系统健康状态成功")

@router.get("/dashboard/user-growth")
async def get_user_growth_data(
    days: int = Query(30, ge=1, le=365),
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取用户增长数据"""
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days-1)
    
    # 查询每日新用户数
    daily_users = db.query(
        func.date(models.User.created_at).label('date'),
        func.count(models.User.id).label('count')
    ).filter(
        func.date(models.User.created_at) >= start_date
    ).group_by(
        func.date(models.User.created_at)
    ).all()
    
    # 生成完整的日期序列
    dates = []
    counts = []
    current_date = start_date
    
    daily_data = {str(item.date): item.count for item in daily_users}
    
    while current_date <= end_date:
        dates.append(current_date.strftime('%Y-%m-%d'))
        counts.append(daily_data.get(str(current_date), 0))
        current_date += timedelta(days=1)
    
    growth_data = {
        "dates": dates,
        "counts": counts
    }
    
    return success_response(data=growth_data, message="获取用户增长数据成功")

@router.get("/dashboard/category-stats")
async def get_category_stats(
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取分类统计"""
    category_stats = db.query(
        models.Product.category,
        func.count(models.Product.id).label('count')
    ).filter(
        models.Product.category.isnot(None)
    ).group_by(
        models.Product.category
    ).all()
    
    categories = [stat.category for stat in category_stats]
    counts = [stat.count for stat in category_stats]
    
    stats_data = {
        "categories": categories,
        "counts": counts
    }
    
    return success_response(data=stats_data, message="获取分类统计成功")

# 用户管理接口
@router.get("/users")
async def get_users(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取用户列表"""
    query = db.query(models.User)
    
    if search:
        query = query.filter(
            or_(
                models.User.username.contains(search),
                models.User.email.contains(search),
                models.User.full_name.contains(search)
            )
        )
    
    total = query.count()
    users = query.offset((page - 1) * size).limit(size).all()
    
    users_data = []
    for user in users:
        users_data.append({
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "phone": user.phone,
            "avatar_url": user.avatar_url,
            "role": user.role.value,
            "is_active": user.is_active,
            "created_at": user.created_at.isoformat()
        })
    
    return list_response(
        data=users_data,
        total=total,
        page=page,
        page_size=size,
        message="获取用户列表成功"
    )

@router.put("/users/{user_id}/status")
async def toggle_user_status(
    user_id: int,
    status_data: dict,
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """切换用户状态"""
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        return error_response(message="用户不存在", code=StatusCode.NOT_FOUND)
    
    user.is_active = status_data.get("is_active", user.is_active)
    db.commit()
    db.refresh(user)
    
    user_data = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "role": user.role.value,
        "is_active": user.is_active,
        "created_at": user.created_at.isoformat()
    }
    
    return success_response(data=user_data, message="用户状态更新成功")

@router.put("/users/{user_id}/admin")
async def toggle_admin_status(
    user_id: int,
    admin_data: dict,
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """切换管理员权限"""
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        return error_response(message="用户不存在", code=StatusCode.NOT_FOUND)
    
    # 防止删除最后一个管理员
    if user.role == models.UserRole.ADMIN and not admin_data.get("is_admin", True):
        admin_count = db.query(models.User).filter(models.User.role == models.UserRole.ADMIN).count()
        if admin_count <= 1:
            return error_response(message="不能删除最后一个管理员", code=StatusCode.BAD_REQUEST)
    
    user.role = models.UserRole.ADMIN if admin_data.get("is_admin", False) else models.UserRole.USER
    db.commit()
    db.refresh(user)
    
    user_data = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "role": user.role.value,
        "is_active": user.is_active,
        "created_at": user.created_at.isoformat()
    }
    
    return success_response(data=user_data, message="管理员权限更新成功")

# 商品管理接口
@router.get("/products")
async def get_products(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取商品列表"""
    query = db.query(models.Product)

    if search:
        query = query.filter(
            or_(
                models.Product.title.contains(search),
                models.Product.description.contains(search)
            )
        )

    if category:
        query = query.filter(models.Product.category == category)

    total = query.count()
    products = query.order_by(models.Product.created_at.desc()).offset((page - 1) * size).limit(size).all()

    products_data = []
    for product in products:
        # 获取商品所有者信息
        owner = db.query(models.User).filter(models.User.id == product.owner_id).first()

        products_data.append({
            "id": product.id,
            "title": product.title,
            "description": product.description,
            "price": float(product.price),
            "image_url": product.image_url,
            "category": product.category,
            "is_available": product.is_available,
            "owner_id": product.owner_id,
            "owner_username": owner.username if owner else "未知用户",
            "created_at": product.created_at.isoformat()
        })

    return list_response(
        data=products_data,
        total=total,
        page=page,
        page_size=size,
        message="获取商品列表成功"
    )

@router.put("/products/{product_id}/status")
async def toggle_product_status(
    product_id: int,
    status_data: dict,
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """切换商品状态"""
    product = db.query(models.Product).filter(models.Product.id == product_id).first()
    if not product:
        return error_response(message="商品不存在", code=StatusCode.NOT_FOUND)

    product.is_available = status_data.get("is_available", product.is_available)
    db.commit()
    db.refresh(product)

    product_data = {
        "id": product.id,
        "title": product.title,
        "description": product.description,
        "price": float(product.price),
        "image_url": product.image_url,
        "category": product.category,
        "is_available": product.is_available,
        "owner_id": product.owner_id,
        "created_at": product.created_at.isoformat()
    }

    return success_response(data=product_data, message="商品状态更新成功")

@router.get("/products/{product_id}")
async def get_product(
    product_id: int,
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取单个商品详情"""
    product = db.query(models.Product).filter(models.Product.id == product_id).first()
    if not product:
        return error_response(message="商品不存在", code=StatusCode.NOT_FOUND)

    # 获取商品所有者信息
    owner = db.query(models.User).filter(models.User.id == product.owner_id).first()

    product_data = {
        "id": product.id,
        "title": product.title,
        "description": product.description,
        "price": float(product.price),
        "image_url": product.image_url,
        "category": product.category,
        "is_available": product.is_available,
        "owner_id": product.owner_id,
        "owner_username": owner.username if owner else "未知用户",
        "created_at": product.created_at.isoformat()
    }

    return success_response(data=product_data, message="获取商品详情成功")

@router.post("/products")
async def create_product(
    product_data: dict,
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """创建商品"""
    db_product = models.Product(
        title=product_data.get("title"),
        description=product_data.get("description"),
        price=product_data.get("price"),
        image_url=product_data.get("image_url"),
        category=product_data.get("category"),
        owner_id=admin_user.id  # 管理员创建的商品
    )
    db.add(db_product)
    db.commit()
    db.refresh(db_product)

    product_response = {
        "id": db_product.id,
        "title": db_product.title,
        "description": db_product.description,
        "price": float(db_product.price),
        "image_url": db_product.image_url,
        "category": db_product.category,
        "is_available": db_product.is_available,
        "owner_id": db_product.owner_id,
        "owner_username": admin_user.username,
        "created_at": db_product.created_at.isoformat()
    }

    return success_response(data=product_response, message="商品创建成功")

@router.put("/products/{product_id}")
async def update_product(
    product_id: int,
    product_data: dict,
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新商品"""
    product = db.query(models.Product).filter(models.Product.id == product_id).first()
    if not product:
        return error_response(message="商品不存在", code=StatusCode.NOT_FOUND)

    # 更新商品信息
    if "title" in product_data:
        product.title = product_data["title"]
    if "description" in product_data:
        product.description = product_data["description"]
    if "price" in product_data:
        product.price = product_data["price"]
    if "image_url" in product_data:
        product.image_url = product_data["image_url"]
    if "category" in product_data:
        product.category = product_data["category"]
    if "is_available" in product_data:
        product.is_available = product_data["is_available"]

    db.commit()
    db.refresh(product)

    # 获取商品所有者信息
    owner = db.query(models.User).filter(models.User.id == product.owner_id).first()

    product_response = {
        "id": product.id,
        "title": product.title,
        "description": product.description,
        "price": float(product.price),
        "image_url": product.image_url,
        "category": product.category,
        "is_available": product.is_available,
        "owner_id": product.owner_id,
        "owner_username": owner.username if owner else "未知用户",
        "created_at": product.created_at.isoformat()
    }

    return success_response(data=product_response, message="商品更新成功")

@router.delete("/products/{product_id}")
async def delete_product(
    product_id: int,
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """删除商品"""
    product = db.query(models.Product).filter(models.Product.id == product_id).first()
    if not product:
        return error_response(message="商品不存在", code=StatusCode.NOT_FOUND)

    db.delete(product)
    db.commit()

    return success_response(data={"id": product_id}, message="商品删除成功")

# 交易管理接口
@router.get("/transactions")
async def get_transactions(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取交易列表"""
    query = db.query(models.Transaction)

    if status:
        try:
            status_enum = models.TransactionStatus(status)
            query = query.filter(models.Transaction.status == status_enum)
        except ValueError:
            pass

    total = query.count()
    transactions = query.order_by(models.Transaction.created_at.desc()).offset((page - 1) * size).limit(size).all()

    transactions_data = []
    for transaction in transactions:
        # 获取买家、卖家和商品信息
        buyer = db.query(models.User).filter(models.User.id == transaction.buyer_id).first()
        seller = db.query(models.User).filter(models.User.id == transaction.seller_id).first()
        product = db.query(models.Product).filter(models.Product.id == transaction.product_id).first()

        transactions_data.append({
            "id": transaction.id,
            "amount": float(transaction.amount),
            "status": transaction.status.value,
            "buyer_id": transaction.buyer_id,
            "buyer_username": buyer.username if buyer else "未知用户",
            "seller_id": transaction.seller_id,
            "seller_username": seller.username if seller else "未知用户",
            "product_id": transaction.product_id,
            "product_title": product.title if product else "未知商品",
            "created_at": transaction.created_at.isoformat()
        })

    return list_response(
        data=transactions_data,
        total=total,
        page=page,
        page_size=size,
        message="获取交易列表成功"
    )

# 聊天管理接口
@router.get("/chat/users")
async def get_chat_users(
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取有聊天记录的用户列表"""
    # 获取所有非管理员用户
    users = db.query(models.User).filter(models.User.role != models.UserRole.ADMIN).all()

    users_data = []
    for user in users:
        users_data.append({
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": user.role.value,
            "is_active": user.is_active,
            "created_at": user.created_at.isoformat()
        })

    return success_response(data=users_data, message="获取聊天用户列表成功")

@router.get("/chat/messages/{user_id}")
async def get_chat_messages(
    user_id: int,
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取与特定用户的聊天记录"""
    # 检查用户是否存在
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        return error_response(message="用户不存在", code=StatusCode.NOT_FOUND)

    # 这里应该查询聊天消息表，但目前我们还没有聊天消息模型
    # 暂时返回空列表
    messages_data = []

    return list_response(
        data=messages_data,
        total=0,
        page=page,
        page_size=size,
        message="获取聊天记录成功"
    )

@router.post("/chat/send")
async def send_admin_message(
    message_data: dict,
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """管理员发送消息"""
    receiver_id = message_data.get("receiver_id")
    content = message_data.get("content")
    message_type = message_data.get("message_type", "text")

    if not receiver_id or not content:
        return error_response(message="接收者ID和消息内容不能为空", code=StatusCode.BAD_REQUEST)

    # 检查接收者是否存在
    receiver = db.query(models.User).filter(models.User.id == receiver_id).first()
    if not receiver:
        return error_response(message="接收者不存在", code=StatusCode.NOT_FOUND)

    # 这里应该创建聊天消息记录，但目前我们还没有聊天消息模型
    # 暂时返回模拟数据
    message_response = {
        "id": 1,
        "sender_id": admin_user.id,
        "receiver_id": receiver_id,
        "content": content,
        "message_type": message_type,
        "created_at": "2025-06-27T14:00:00"
    }

    return success_response(data=message_response, message="消息发送成功")

@router.get("/chat/unread-stats")
async def get_unread_stats(
    admin_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """获取未读消息统计"""
    # 这里应该查询未读消息统计，但目前我们还没有聊天消息模型
    # 暂时返回模拟数据
    stats_data = {
        "total_unread": 0,
        "conversations_with_unread": 0
    }

    return success_response(data=stats_data, message="获取未读消息统计成功")
