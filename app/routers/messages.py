from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from typing import List, Optional
from app.database import get_db
from app import models, schemas, auth

router = APIRouter()


@router.post("/", response_model=schemas.Message)
async def create_message(
    message: schemas.MessageCreate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建消息"""
    # 如果有接收者，检查接收者是否存在
    if message.receiver_id:
        receiver = db.query(models.User).filter(models.User.id == message.receiver_id).first()
        if not receiver:
            raise HTTPException(status_code=404, detail="接收者不存在")
    
    db_message = models.Message(
        sender_id=current_user.id,
        receiver_id=message.receiver_id,
        title=message.title,
        content=message.content,
        message_type=message.message_type
    )
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    
    return db_message


@router.get("/", response_model=List[schemas.Message])
async def list_messages(
    skip: int = 0,
    limit: int = 100,
    message_type: Optional[str] = None,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取消息列表"""
    query = db.query(models.Message).filter(
        or_(
            models.Message.receiver_id == current_user.id,
            models.Message.sender_id == current_user.id
        )
    )
    
    if message_type:
        query = query.filter(models.Message.message_type == message_type)
    
    messages = query.order_by(models.Message.created_at.desc()).offset(skip).limit(limit).all()
    return messages


@router.get("/conversations")
async def get_conversations(
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取会话列表"""
    # 获取与当前用户相关的所有聊天消息
    messages = db.query(models.Message).filter(
        and_(
            models.Message.message_type == models.MessageType.CHAT,
            or_(
                models.Message.receiver_id == current_user.id,
                models.Message.sender_id == current_user.id
            )
        )
    ).order_by(models.Message.created_at.desc()).all()
    
    # 按对话伙伴分组
    conversations = {}
    for message in messages:
        # 确定对话伙伴
        partner_id = message.receiver_id if message.sender_id == current_user.id else message.sender_id
        
        if partner_id not in conversations:
            # 获取伙伴信息
            partner = db.query(models.User).filter(models.User.id == partner_id).first()
            conversations[partner_id] = {
                "partner_id": partner_id,
                "partner_username": partner.username if partner else "未知用户",
                "partner_full_name": partner.full_name if partner else "未知用户",
                "partner_avatar_url": partner.avatar_url if partner else None,
                "last_message": message.content,
                "last_message_time": message.created_at,
                "unread_count": 0
            }
        
        # 计算未读消息数（只计算接收到的消息）
        if message.receiver_id == current_user.id and not message.is_read:
            conversations[partner_id]["unread_count"] += 1
    
    return {"conversations": list(conversations.values())}


@router.get("/conversation/{partner_id}")
async def get_conversation_messages(
    partner_id: int,
    skip: int = 0,
    limit: int = 50,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取与指定用户的聊天记录"""
    messages = db.query(models.Message).filter(
        and_(
            models.Message.message_type == models.MessageType.CHAT,
            or_(
                and_(
                    models.Message.sender_id == current_user.id,
                    models.Message.receiver_id == partner_id
                ),
                and_(
                    models.Message.sender_id == partner_id,
                    models.Message.receiver_id == current_user.id
                )
            )
        )
    ).order_by(models.Message.created_at.desc()).offset(skip).limit(limit).all()
    
    # 标记接收到的消息为已读
    unread_messages = db.query(models.Message).filter(
        and_(
            models.Message.sender_id == partner_id,
            models.Message.receiver_id == current_user.id,
            models.Message.is_read == False
        )
    ).all()
    
    for message in unread_messages:
        message.is_read = True
    
    db.commit()
    
    return {"messages": messages[::-1]}  # 返回正序消息


@router.get("/{message_id}", response_model=schemas.Message)
async def get_message(
    message_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取消息详情"""
    message = db.query(models.Message).filter(models.Message.id == message_id).first()
    if not message:
        raise HTTPException(status_code=404, detail="消息不存在")
    
    # 检查权限
    if message.sender_id != current_user.id and message.receiver_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此消息")
    
    # 如果是接收者查看消息，标记为已读
    if message.receiver_id == current_user.id and not message.is_read:
        message.is_read = True
        db.commit()
    
    return message


@router.put("/{message_id}/read")
async def mark_message_read(
    message_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """标记消息为已读"""
    message = db.query(models.Message).filter(models.Message.id == message_id).first()
    if not message:
        raise HTTPException(status_code=404, detail="消息不存在")
    
    # 只有接收者可以标记消息为已读
    if message.receiver_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此消息")
    
    message.is_read = True
    db.commit()
    
    return {"message": "消息已标记为已读"}
