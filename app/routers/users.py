from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON>F<PERSON>
from sqlalchemy.orm import Session
from app.database import get_db
from app import models, schemas, auth
from app.config import settings
from app.response_utils import (
    success_response, error_response, create_response,
    list_response, not_found_response, forbidden_response,
    StatusCode, ResponseMessage
)

router = APIRouter()


@router.post("/register")
async def register_user(user: schemas.UserCreate, db: Session = Depends(get_db)):
    """用户注册"""
    # 检查用户名是否已存在
    db_user = db.query(models.User).filter(models.User.username == user.username).first()
    if db_user:
        return error_response(message="用户名已存在", code=StatusCode.BAD_REQUEST)

    # 检查邮箱是否已存在
    db_user = db.query(models.User).filter(models.User.email == user.email).first()
    if db_user:
        return error_response(message="邮箱已被注册", code=StatusCode.BAD_REQUEST)

    # 创建新用户
    hashed_password = auth.get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        phone=user.phone,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # 转换为字典格式
    user_data = {
        "id": db_user.id,
        "username": db_user.username,
        "email": db_user.email,
        "full_name": db_user.full_name,
        "phone": db_user.phone,
        "avatar_url": db_user.avatar_url,
        "role": db_user.role.value,
        "is_active": db_user.is_active,
        "created_at": db_user.created_at.isoformat()
    }

    return create_response(data=user_data, message=ResponseMessage.USER_CREATED)


@router.post("/login")
async def login_user(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """用户登录"""
    user = auth.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        return error_response(message=ResponseMessage.LOGIN_FAILED, code=StatusCode.UNAUTHORIZED)

    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = auth.create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    token_data = {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": user.role.value
        }
    }

    return success_response(data=token_data, message=ResponseMessage.LOGIN_SUCCESS)


@router.get("/me")
async def read_users_me(current_user: models.User = Depends(auth.get_current_active_user)):
    """获取当前用户信息"""
    user_data = {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "phone": current_user.phone,
        "avatar_url": current_user.avatar_url,
        "role": current_user.role.value,
        "is_active": current_user.is_active,
        "created_at": current_user.created_at.isoformat()
    }
    return success_response(data=user_data, message="获取用户信息成功")


@router.put("/me", response_model=schemas.User)
async def update_user_me(
    user_update: schemas.UserUpdate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新当前用户信息"""
    for field, value in user_update.dict(exclude_unset=True).items():
        setattr(current_user, field, value)
    
    db.commit()
    db.refresh(current_user)
    return current_user


@router.get("/")
async def list_users(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户列表"""
    # 获取总数
    total = db.query(models.User).count()

    # 获取用户列表
    users = db.query(models.User).offset(skip).limit(limit).all()

    # 转换为字典格式
    users_data = []
    for user in users:
        users_data.append({
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "phone": user.phone,
            "avatar_url": user.avatar_url,
            "role": user.role.value,
            "is_active": user.is_active,
            "created_at": user.created_at.isoformat()
        })

    return list_response(
        data=users_data,
        total=total,
        page=skip // limit + 1 if limit > 0 else 1,
        page_size=limit,
        message="获取用户列表成功"
    )


@router.get("/{user_id}")
async def get_user(
    user_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取指定用户信息"""
    user = db.query(models.User).filter(models.User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=StatusCode.NOT_FOUND,
            detail=error_response(message=ResponseMessage.USER_NOT_FOUND, code=StatusCode.NOT_FOUND)
        )

    user_data = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "phone": user.phone,
        "avatar_url": user.avatar_url,
        "role": user.role.value,
        "is_active": user.is_active,
        "created_at": user.created_at.isoformat()
    }

    return success_response(data=user_data, message="获取用户信息成功")
