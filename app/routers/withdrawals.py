from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from app.database import get_db
from app import models, schemas, auth
from app.response_utils import (
    success_response, error_response, create_response, update_response,
    list_response, not_found_response, forbidden_response,
    StatusCode, ResponseMessage
)

router = APIRouter()


@router.post("/")
async def create_withdrawal(
    withdrawal: schemas.WithdrawalCreate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建提现申请"""
    try:
        # 创建提现记录
        db_withdrawal = models.Withdrawal(
            user_id=current_user.id,
            amount=withdrawal.amount,
            currency=withdrawal.currency,
            bank_account=withdrawal.bank_account,
            bank_name=withdrawal.bank_name,
            account_name=withdrawal.account_name,
            notes=withdrawal.notes,
            status=models.WithdrawalStatus.PENDING
        )
        db.add(db_withdrawal)
        db.commit()
        db.refresh(db_withdrawal)
        
        # 构造响应数据
        withdrawal_data = {
            "id": db_withdrawal.id,
            "user_id": db_withdrawal.user_id,
            "amount": float(db_withdrawal.amount),
            "currency": db_withdrawal.currency.value,
            "bank_account": db_withdrawal.bank_account,
            "bank_name": db_withdrawal.bank_name,
            "account_name": db_withdrawal.account_name,
            "notes": db_withdrawal.notes,
            "status": db_withdrawal.status.value,
            "created_at": db_withdrawal.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return success_response(
            message="提现申请创建成功",
            data=withdrawal_data
        )
        
    except Exception as e:
        db.rollback()
        return error_response(
            message=f"创建提现申请失败: {str(e)}"
        )


@router.get("/")
async def list_withdrawals(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取提现记录列表"""
    try:
        query = db.query(models.Withdrawal).filter(models.Withdrawal.user_id == current_user.id)
        
        if status:
            try:
                status_enum = models.WithdrawalStatus(status)
                query = query.filter(models.Withdrawal.status == status_enum)
            except ValueError:
                return error_response(message="无效的状态值")
        
        withdrawals = query.order_by(models.Withdrawal.created_at.desc()).offset(skip).limit(limit).all()
        
        # 构造响应数据
        withdrawals_data = []
        for withdrawal in withdrawals:
            withdrawal_data = {
                "id": withdrawal.id,
                "user_id": withdrawal.user_id,
                "amount": float(withdrawal.amount),
                "currency": withdrawal.currency.value,
                "bank_account": withdrawal.bank_account,
                "bank_name": withdrawal.bank_name,
                "account_name": withdrawal.account_name,
                "notes": withdrawal.notes,
                "status": withdrawal.status.value,
                "processed_at": withdrawal.processed_at.strftime("%Y-%m-%d %H:%M:%S") if withdrawal.processed_at else None,
                "created_at": withdrawal.created_at.strftime("%Y-%m-%d %H:%M:%S")
            }
            withdrawals_data.append(withdrawal_data)
        
        return success_response(
            message="获取提现记录成功",
            data=withdrawals_data
        )
        
    except Exception as e:
        return error_response(
            message=f"获取提现记录失败: {str(e)}"
        )


@router.get("/{withdrawal_id}")
async def get_withdrawal(
    withdrawal_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取提现记录详情"""
    try:
        withdrawal = db.query(models.Withdrawal).filter(
            models.Withdrawal.id == withdrawal_id,
            models.Withdrawal.user_id == current_user.id
        ).first()
        
        if not withdrawal:
            return not_found_response(message="提现记录不存在")
        
        # 构造响应数据
        withdrawal_data = {
            "id": withdrawal.id,
            "user_id": withdrawal.user_id,
            "amount": float(withdrawal.amount),
            "currency": withdrawal.currency.value,
            "bank_account": withdrawal.bank_account,
            "bank_name": withdrawal.bank_name,
            "account_name": withdrawal.account_name,
            "notes": withdrawal.notes,
            "status": withdrawal.status.value,
            "processed_at": withdrawal.processed_at.strftime("%Y-%m-%d %H:%M:%S") if withdrawal.processed_at else None,
            "created_at": withdrawal.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return success_response(
            message="获取提现记录详情成功",
            data=withdrawal_data
        )
        
    except Exception as e:
        return error_response(
            message=f"获取提现记录详情失败: {str(e)}"
        )


# 管理员接口
@router.get("/admin/all")
async def admin_list_withdrawals(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """管理员获取所有提现记录"""
    # 检查管理员权限
    if current_user.role != models.UserRole.ADMIN:
        return forbidden_response(message="需要管理员权限")
    
    try:
        query = db.query(models.Withdrawal)
        
        if status:
            try:
                status_enum = models.WithdrawalStatus(status)
                query = query.filter(models.Withdrawal.status == status_enum)
            except ValueError:
                return error_response(message="无效的状态值")
        
        withdrawals = query.order_by(models.Withdrawal.created_at.desc()).offset(skip).limit(limit).all()
        
        # 构造响应数据
        withdrawals_data = []
        for withdrawal in withdrawals:
            # 获取用户信息
            user = db.query(models.User).filter(models.User.id == withdrawal.user_id).first()
            
            withdrawal_data = {
                "id": withdrawal.id,
                "user_id": withdrawal.user_id,
                "username": user.username if user else None,
                "amount": float(withdrawal.amount),
                "currency": withdrawal.currency.value,
                "bank_account": withdrawal.bank_account,
                "bank_name": withdrawal.bank_name,
                "account_name": withdrawal.account_name,
                "notes": withdrawal.notes,
                "status": withdrawal.status.value,
                "processed_at": withdrawal.processed_at.strftime("%Y-%m-%d %H:%M:%S") if withdrawal.processed_at else None,
                "created_at": withdrawal.created_at.strftime("%Y-%m-%d %H:%M:%S")
            }
            withdrawals_data.append(withdrawal_data)
        
        return success_response(
            message="获取所有提现记录成功",
            data=withdrawals_data
        )
        
    except Exception as e:
        return error_response(
            message=f"获取提现记录失败: {str(e)}"
        )


@router.put("/admin/{withdrawal_id}")
async def admin_update_withdrawal(
    withdrawal_id: int,
    withdrawal_update: schemas.WithdrawalUpdate,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """管理员更新提现状态"""
    # 检查管理员权限
    if current_user.role != models.UserRole.ADMIN:
        return forbidden_response(message="需要管理员权限")
    
    try:
        withdrawal = db.query(models.Withdrawal).filter(models.Withdrawal.id == withdrawal_id).first()
        
        if not withdrawal:
            return not_found_response(message="提现记录不存在")
        
        # 更新字段
        if withdrawal_update.status is not None:
            withdrawal.status = withdrawal_update.status
            if withdrawal_update.status in [models.WithdrawalStatus.COMPLETED, models.WithdrawalStatus.REJECTED]:
                withdrawal.processed_at = datetime.now()
        
        if withdrawal_update.notes is not None:
            withdrawal.notes = withdrawal_update.notes
        
        db.commit()
        db.refresh(withdrawal)
        
        # 构造响应数据
        withdrawal_data = {
            "id": withdrawal.id,
            "user_id": withdrawal.user_id,
            "amount": float(withdrawal.amount),
            "currency": withdrawal.currency.value,
            "bank_account": withdrawal.bank_account,
            "bank_name": withdrawal.bank_name,
            "account_name": withdrawal.account_name,
            "notes": withdrawal.notes,
            "status": withdrawal.status.value,
            "processed_at": withdrawal.processed_at.strftime("%Y-%m-%d %H:%M:%S") if withdrawal.processed_at else None,
            "created_at": withdrawal.created_at.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return success_response(
            message="提现状态更新成功",
            data=withdrawal_data
        )
        
    except Exception as e:
        db.rollback()
        return error_response(
            message=f"更新提现状态失败: {str(e)}"
        )
