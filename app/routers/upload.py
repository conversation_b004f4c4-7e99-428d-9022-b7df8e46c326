from fastapi import APIRouter, Depends, UploadFile, File
from sqlalchemy.orm import Session
import os
import uuid
from app.database import get_db
from app import models, auth
from app.response_utils import (
    success_response, error_response
)

router = APIRouter()


@router.post("/image")
async def upload_image(
    file: UploadFile = File(...),
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """上传图片，返回图片URL"""
    try:
        # 验证文件
        if not file.filename:
            return error_response(message="请选择要上传的文件")

        # 验证文件类型
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
        if file.content_type not in allowed_types:
            return error_response(message="不支持的文件类型，请上传图片文件（jpg, png, gif）")

        # 验证文件大小（5MB限制）
        content = await file.read()
        if len(content) > 5 * 1024 * 1024:  # 5MB
            return error_response(message="文件大小不能超过5MB")

        # 创建上传目录
        upload_dir = "uploads/images"
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)

        # 保存文件
        with open(file_path, "wb") as buffer:
            buffer.write(content)

        # 构造图片URL
        image_url = f"/uploads/images/{unique_filename}"

        return success_response(
            message="图片上传成功",
            data={
                "image_url": image_url,
                "filename": unique_filename,
                "original_filename": file.filename,
                "file_size": len(content)
            }
        )

    except Exception as e:
        # 如果出错，删除已上传的文件
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except:
                pass
        return error_response(
            message=f"图片上传失败: {str(e)}"
        )
