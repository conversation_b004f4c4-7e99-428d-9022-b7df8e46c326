from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app import models, schemas, auth
from app.routers.admin import get_admin_user
from app.response_utils import success_response, error_response
import uuid
import os
from pathlib import Path

router = APIRouter()

# 确保上传目录存在
UPLOAD_DIR = Path("uploads/currencies")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)


@router.get("/", response_model=dict)
async def get_currency_info_list(
    page: int = 1,
    size: int = 20,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """获取货币信息列表"""
    try:
        query = db.query(models.CurrencyInfo)
        
        if is_active is not None:
            query = query.filter(models.CurrencyInfo.is_active == is_active)
        
        # 按排序顺序和代码排序
        query = query.order_by(models.CurrencyInfo.sort_order, models.CurrencyInfo.code)
        
        # 分页
        total = query.count()
        currencies = query.offset((page - 1) * size).limit(size).all()
        
        currencies_data = []
        for currency in currencies:
            currencies_data.append({
                "id": currency.id,
                "code": currency.code,
                "name": currency.name,
                "symbol": currency.symbol,
                "icon_url": currency.icon_url,
                "is_active": currency.is_active,
                "sort_order": currency.sort_order,
                "created_at": currency.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": currency.updated_at.strftime("%Y-%m-%d %H:%M:%S") if currency.updated_at else None
            })
        
        return success_response(
            message="获取货币信息列表成功",
            data={
                "currencies": currencies_data,
                "total": total,
                "page": page,
                "page_size": size
            }
        )
    except Exception as e:
        return error_response(message=f"获取货币信息列表失败: {str(e)}", code=500)


@router.get("/active", response_model=dict)
async def get_active_currencies(db: Session = Depends(get_db)):
    """获取所有启用的货币（用于前端选择）"""
    try:
        currencies = db.query(models.CurrencyInfo).filter(
            models.CurrencyInfo.is_active == True
        ).order_by(models.CurrencyInfo.sort_order, models.CurrencyInfo.code).all()
        
        currencies_data = []
        for currency in currencies:
            currencies_data.append({
                "id": currency.id,
                "code": currency.code,
                "name": currency.name,
                "symbol": currency.symbol,
                "icon_url": currency.icon_url
            })
        
        return success_response(
            message="获取启用货币成功",
            data={"currencies": currencies_data}
        )
    except Exception as e:
        return error_response(message=f"获取启用货币失败: {str(e)}", code=500)


@router.post("/", response_model=dict)
async def create_currency_info(
    code: str = Form(...),
    name: str = Form(...),
    symbol: str = Form(...),
    sort_order: int = Form(0),
    is_active: bool = Form(True),
    icon: Optional[UploadFile] = File(None),
    current_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """创建货币信息（管理员专用）"""
    try:
        # 检查货币代码是否已存在
        existing = db.query(models.CurrencyInfo).filter(
            models.CurrencyInfo.code == code.upper()
        ).first()
        if existing:
            return error_response(message="货币代码已存在", code=400)
        
        icon_url = None
        if icon:
            # 验证文件类型
            if not icon.content_type.startswith('image/'):
                return error_response(message="只能上传图片文件", code=400)
            
            # 生成唯一文件名
            file_extension = icon.filename.split('.')[-1] if '.' in icon.filename else 'jpg'
            unique_filename = f"{uuid.uuid4()}.{file_extension}"
            file_path = UPLOAD_DIR / unique_filename
            
            # 保存文件
            with open(file_path, "wb") as buffer:
                content = await icon.read()
                buffer.write(content)
            
            icon_url = f"/uploads/currencies/{unique_filename}"
        
        # 创建货币信息
        currency = models.CurrencyInfo(
            code=code.upper(),
            name=name,
            symbol=symbol,
            icon_url=icon_url,
            sort_order=sort_order,
            is_active=is_active
        )
        
        db.add(currency)
        db.commit()
        db.refresh(currency)
        
        return success_response(
            message="货币信息创建成功",
            data={
                "id": currency.id,
                "code": currency.code,
                "name": currency.name,
                "symbol": currency.symbol,
                "icon_url": currency.icon_url,
                "sort_order": currency.sort_order,
                "is_active": currency.is_active,
                "created_at": currency.created_at.strftime("%Y-%m-%d %H:%M:%S")
            }
        )
    except Exception as e:
        db.rollback()
        return error_response(message=f"创建货币信息失败: {str(e)}", code=500)


@router.put("/{currency_id}", response_model=dict)
async def update_currency_info(
    currency_id: int,
    code: Optional[str] = Form(None),
    name: Optional[str] = Form(None),
    symbol: Optional[str] = Form(None),
    sort_order: Optional[int] = Form(None),
    is_active: Optional[bool] = Form(None),
    icon: Optional[UploadFile] = File(None),
    current_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """更新货币信息（管理员专用）"""
    try:
        currency = db.query(models.CurrencyInfo).filter(
            models.CurrencyInfo.id == currency_id
        ).first()
        if not currency:
            return error_response(message="货币信息不存在", code=404)
        
        # 检查货币代码冲突
        if code and code.upper() != currency.code:
            existing = db.query(models.CurrencyInfo).filter(
                models.CurrencyInfo.code == code.upper(),
                models.CurrencyInfo.id != currency_id
            ).first()
            if existing:
                return error_response(message="货币代码已存在", code=400)
        
        # 处理图标上传
        if icon:
            # 验证文件类型
            if not icon.content_type.startswith('image/'):
                return error_response(message="只能上传图片文件", code=400)
            
            # 删除旧图标
            if currency.icon_url:
                old_file_path = Path(f"uploads{currency.icon_url.replace('/uploads', '')}")
                if old_file_path.exists():
                    old_file_path.unlink()
            
            # 保存新图标
            file_extension = icon.filename.split('.')[-1] if '.' in icon.filename else 'jpg'
            unique_filename = f"{uuid.uuid4()}.{file_extension}"
            file_path = UPLOAD_DIR / unique_filename
            
            with open(file_path, "wb") as buffer:
                content = await icon.read()
                buffer.write(content)
            
            currency.icon_url = f"/uploads/currencies/{unique_filename}"
        
        # 更新字段
        if code is not None:
            currency.code = code.upper()
        if name is not None:
            currency.name = name
        if symbol is not None:
            currency.symbol = symbol
        if sort_order is not None:
            currency.sort_order = sort_order
        if is_active is not None:
            currency.is_active = is_active
        
        db.commit()
        db.refresh(currency)
        
        return success_response(
            message="货币信息更新成功",
            data={
                "id": currency.id,
                "code": currency.code,
                "name": currency.name,
                "symbol": currency.symbol,
                "icon_url": currency.icon_url,
                "sort_order": currency.sort_order,
                "is_active": currency.is_active,
                "updated_at": currency.updated_at.strftime("%Y-%m-%d %H:%M:%S") if currency.updated_at else None
            }
        )
    except Exception as e:
        db.rollback()
        return error_response(message=f"更新货币信息失败: {str(e)}", code=500)


@router.delete("/{currency_id}", response_model=dict)
async def delete_currency_info(
    currency_id: int,
    current_user: models.User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """删除货币信息（管理员专用）"""
    try:
        currency = db.query(models.CurrencyInfo).filter(
            models.CurrencyInfo.id == currency_id
        ).first()
        if not currency:
            return error_response(message="货币信息不存在", code=404)
        
        # 删除图标文件
        if currency.icon_url:
            file_path = Path(f"uploads{currency.icon_url.replace('/uploads', '')}")
            if file_path.exists():
                file_path.unlink()
        
        db.delete(currency)
        db.commit()
        
        return success_response(message="货币信息删除成功")
    except Exception as e:
        db.rollback()
        return error_response(message=f"删除货币信息失败: {str(e)}", code=500)
