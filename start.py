#!/usr/bin/env python3
"""
项目启动脚本
"""

import uvicorn
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🚀 启动小黑后端服务...")
    print("📝 API 文档地址: http://localhost:8000/docs")
    print("🔄 WebSocket 聊天地址: ws://localhost:8000/api/v1/chat/ws/{user_id}?token={access_token}")
    print("⚡ 服务器将在 http://localhost:8000 启动")
    print("-" * 50)
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
